{"name": "cmsassstarter", "version": "0.0.1", "private": true, "scripts": {"dev": "vite dev", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "test": "vitest", "test_run": "vitest run", "lint": "eslint .", "format": "prettier --write --plugin prettier-plugin-svelte ./", "format_check": "prettier --check --plugin prettier-plugin-svelte ./", "clean": "rm -rf node_modules .svelte-kit build dist .vercel && pnpm install"}, "devDependencies": {"@21st-extension/toolbar": "^0.5.14", "@sveltejs/adapter-vercel": "^5.7.2", "@sveltejs/kit": "^2.22.2", "@sveltejs/vite-plugin-svelte": "^4.0.4", "@tailwindcss/postcss": "^4.1.11", "@tailwindcss/typography": "0.5.0-alpha.3", "@types/glob": "^8.1.0", "@types/html-to-text": "^9.0.4", "@types/jsdom": "^21.1.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.21", "bufferutil": "^4.0.9", "daisyui": "^5.0.43", "encoding": "^0.1.13", "eslint": "^8.57.1", "eslint-config-prettier": "^9.1.0", "eslint-plugin-svelte": "^2.46.1", "fuse.js": "^7.1.0", "gray-matter": "^4.0.3", "html-to-text": "^9.0.5", "jsdom": "^24.1.3", "marked": "^16.0.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-svelte": "^3.4.0", "svelte": "^5.34.9", "svelte-check": "^4.2.2", "sveltekit-superforms": "^2.27.1", "tailwindcss": "^4.0.0", "typescript": "^5.8.3", "utf-8-validate": "^6.0.5", "vite": "^5.4.19", "vitest": "^1.6.1", "zod": "^3.25.67"}, "type": "module", "dependencies": {"@ai-sdk/anthropic": "^1.2.12", "@ai-sdk/google": "^1.2.22", "@ai-sdk/openai": "^1.3.22", "@ai-sdk/openai-compatible": "^0.2.14", "@mastra/core": "^0.10.10", "@motionone/svelte": "^10.16.4", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/auth-ui-svelte": "^0.2.9", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.50.2", "bits-ui": "^0.21.16", "clsx": "^2.1.1", "exa-js": "^1.8.19", "formsnap": "^1.0.1", "glob": "^9.3.5", "lucide-svelte": "^0.436.0", "resend": "^3.5.0", "stripe": "^13.11.0", "tailwind-merge": "^2.6.0", "tailwind-variants": "^0.2.1", "vis-network": "^10.0.1"}}