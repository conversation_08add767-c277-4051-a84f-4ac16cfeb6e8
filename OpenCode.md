# OpenCode.md

## Build, Lint, and Test Commands

### Package Management
- **Install dependencies**: `pnpm install`
- **Remove build artifacts**: `pnpm run clean`

### Development
- **Start development server**: `pnpm run dev`
- **Build for production**: `pnpm run build`
- **Preview production build**: `pnpm run preview`

### Testing & Quality
- **Run all tests**: `pnpm run test`
- **Run a single test**: `pnpm run test -- filename.test.ts`
- **Perform linting**: `pnpm run lint`
- **Apply code formatting**: `pnpm run format`
- **Check types**: `pnpm run check`

## Code Style Guidelines

### Imports & Formatting
- Use ES6 module syntax for imports.
- Consistently order imports: external libs first, followed by internal files.
- Use Prettier for code formatting.

### Types & Naming Conventions
- Use TypeScript for type definitions.
- Follow camelCase for variables and functions, PascalCase for types and classes.
- Prefix private variables with an underscore `_`.

### Error Handling
- Use try-catch for handling asynchronous operations.
- Return descriptive error messages through the use of custom error types.

### General Guidelines
- Use comments to explain complex logic.
- Keep functions and modules focused on a single responsibility for clarity and maintainability.
