import { 
  loadMarkdownContent, 
  loadContentCollection, 
  getCachedContent, 
  setCachedContent 
} from '$lib/utils/content-loader';
import { CONTENT_PATHS } from '$lib/types/content';
import type { 
  Hero<PERSON>ontent, 
  SectionContent, 
  TeamMemberContent, 
  CaseStudyContent, 
  FeatureContent,
  AgentContent 
} from '$lib/types/content';

/**
 * Home page content service
 */
export class HomeContentService {
  private static instance: HomeContentService;
  
  static getInstance(): HomeContentService {
    if (!this.instance) {
      this.instance = new HomeContentService();
    }
    return this.instance;
  }

  /**
   * Load hero section content
   */
  async getHeroContent(): Promise<HeroContent | null> {
    const cacheKey = 'home-hero';
    const cached = getCachedContent<HeroContent>(cacheKey);
    if (cached) return cached;

    const result = await loadMarkdownContent<HeroContent>('home/hero');
    if (result.success && result.data) {
      setCachedContent(cacheKey, result.data);
      return result.data;
    }
    
    console.error('Failed to load hero content:', result.error);
    return null;
  }

  /**
   * Load section content by name
   */
  async getSectionContent(section: string): Promise<SectionContent | null> {
    const cacheKey = `home-${section}`;
    const cached = getCachedContent<SectionContent>(cacheKey);
    if (cached) return cached;

    const result = await loadMarkdownContent<SectionContent>(`home/${section}`);
    if (result.success && result.data) {
      setCachedContent(cacheKey, result.data);
      return result.data;
    }
    
    console.error(`Failed to load ${section} content:`, result.error);
    return null;
  }

  /**
   * Load all team members
   */
  async getTeamMembers(): Promise<TeamMemberContent[]> {
    const cacheKey = 'team-members';
    const cached = getCachedContent<TeamMemberContent[]>(cacheKey);
    if (cached) return cached;

    const result = await loadContentCollection<TeamMemberContent>(CONTENT_PATHS.TEAM);
    if (result.success && result.data) {
      setCachedContent(cacheKey, result.data);
      return result.data;
    }
    
    console.error('Failed to load team members:', result.error);
    return [];
  }

  /**
   * Load case studies
   */
  async getCaseStudies(): Promise<CaseStudyContent[]> {
    const cacheKey = 'case-studies';
    const cached = getCachedContent<CaseStudyContent[]>(cacheKey);
    if (cached) return cached;

    const result = await loadContentCollection<CaseStudyContent>(CONTENT_PATHS.CASE_STUDIES);
    if (result.success && result.data) {
      // Filter only featured case studies and sort by order
      const featured = result.data.filter(cs => cs.frontmatter.featured);
      setCachedContent(cacheKey, featured);
      return featured;
    }
    
    console.error('Failed to load case studies:', result.error);
    return [];
  }

  /**
   * Load feature content (problems, approach, metrics)
   */
  async getFeatureContent(type: string): Promise<FeatureContent | null> {
    const cacheKey = `features-${type}`;
    const cached = getCachedContent<FeatureContent>(cacheKey);
    if (cached) return cached;

    const result = await loadMarkdownContent<FeatureContent>(`features/${type}`);
    if (result.success && result.data) {
      setCachedContent(cacheKey, result.data);
      return result.data;
    }
    
    console.error(`Failed to load ${type} features:`, result.error);
    return null;
  }

  /**
   * Load marketing agents
   */
  async getMarketingAgents(): Promise<AgentContent[]> {
    const cacheKey = 'marketing-agents';
    const cached = getCachedContent<AgentContent[]>(cacheKey);
    if (cached) return cached;

    const result = await loadContentCollection<AgentContent>(CONTENT_PATHS.AGENTS);
    if (result.success && result.data) {
      // Sort by order
      const sorted = result.data.sort((a, b) => a.frontmatter.order - b.frontmatter.order);
      setCachedContent(cacheKey, sorted);
      return sorted;
    }
    
    console.error('Failed to load marketing agents:', result.error);
    return [];
  }

  /**
   * Load all home page content at once
   */
  async getAllHomeContent() {
    const [
      heroContent,
      problemContent,
      approachContent,
      servicesContent,
      marketingAgentsContent,
      resultsContent,
      storiesContent,
      teamContent,
      ctaContent,
      teamMembers,
      caseStudies,
      marketingAgents,
      problemsFeatures,
      approachFeatures,
      servicesFeatures,
      metricsFeatures
    ] = await Promise.all([
      this.getHeroContent(),
      this.getSectionContent('problem'),
      this.getSectionContent('approach'),
      this.getSectionContent('services'),
      this.getSectionContent('marketing-agents'),
      this.getSectionContent('results'),
      this.getSectionContent('stories'),
      this.getSectionContent('team'),
      this.getSectionContent('cta'),
      this.getTeamMembers(),
      this.getCaseStudies(),
      this.getMarketingAgents(),
      this.getFeatureContent('problems'),
      this.getFeatureContent('approach'),
      this.getFeatureContent('services'),
      this.getFeatureContent('metrics')
    ]);

    return {
      hero: heroContent,
      sections: {
        problem: problemContent,
        approach: approachContent,
        services: servicesContent,
        marketingAgents: marketingAgentsContent,
        results: resultsContent,
        stories: storiesContent,
        team: teamContent,
        cta: ctaContent
      },
      teamMembers,
      caseStudies,
      marketingAgents,
      features: {
        problems: problemsFeatures,
        approach: approachFeatures,
        services: servicesFeatures,
        metrics: metricsFeatures
      }
    };
  }
}

/**
 * Convenience function to get the content service instance
 */
export function getContentService(): HomeContentService {
  return HomeContentService.getInstance();
}