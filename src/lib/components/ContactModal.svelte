<script lang="ts">
  import { createEventDispatcher, onMount, onDestroy } from 'svelte';
  import { X } from 'lucide-svelte';
  
  export let isOpen = false;
  
  const dispatch = createEventDispatcher();
  
  let formData = {
    name: '',
    email: '',
    website: '',
    description: ''
  };
  
  let isSubmitting = false;
  let submitMessage = '';
  let submitError = '';
  
  function closeModal() {
    isOpen = false;
    submitMessage = '';
    submitError = '';
    dispatch('close');
  }
  
  function resetForm() {
    formData = {
      name: '',
      email: '',
      website: '',
      description: ''
    };
    submitMessage = '';
    submitError = '';
  }
  
  async function handleSubmit(event: Event) {
    event.preventDefault();
    
    // Reset previous messages
    submitMessage = '';
    submitError = '';
    isSubmitting = true;
    
    try {
      const response = await fetch('/api/contact', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (result.success) {
        submitMessage = 'Thank you! Your message has been sent successfully. We\'ll get back to you soon.';
        resetForm();
        dispatch('submit', { success: true, data: result.data });
        
        // Auto-close modal after 2 seconds on success
        setTimeout(() => {
          if (isOpen) {
            closeModal();
          }
        }, 2000);
      } else {
        submitError = result.error || 'Failed to send message. Please try again.';
      }
    } catch (error) {
      console.error('Form submission error:', error);
      submitError = 'Network error. Please check your connection and try again.';
    } finally {
      isSubmitting = false;
    }
  }
  
  function handleBackdropClick(event: MouseEvent) {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  }
  
  function handleKeydown(event: KeyboardEvent) {
    if (event.key === 'Escape') {
      closeModal();
    }
  }
  
  // Global escape key listener
  function handleGlobalKeydown(event: KeyboardEvent) {
    if (isOpen && event.key === 'Escape') {
      closeModal();
    }
  }
  
  onMount(() => {
    if (typeof document !== 'undefined') {
      document.addEventListener('keydown', handleGlobalKeydown);
    }
  });
  
  onDestroy(() => {
    if (typeof document !== 'undefined') {
      document.removeEventListener('keydown', handleGlobalKeydown);
    }
  });
</script>

{#if isOpen}
  <!-- Modal backdrop -->
  <div 
    class="fixed inset-0 z-50 flex items-center justify-center p-4"
    style="background: rgba(0, 0, 0, 0.8);"
    on:click={handleBackdropClick}
    on:keydown={handleKeydown}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    tabindex="-1"
  >
    <!-- Modal content -->
    <div 
      class="relative w-full max-w-lg border-2"
      style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-xl);"
      role="document"
    >
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b-2" style="border-color: var(--border);">
        <h2 id="modal-title" class="text-2xl font-black" style="color: var(--foreground);">
          Let's Start Your Growth Story
        </h2>
        <button 
          on:click={closeModal}
          class="p-2 transition-colors hover:opacity-70"
          style="color: var(--muted-foreground);"
          aria-label="Close modal"
        >
          <X class="w-6 h-6" />
        </button>
      </div>
      
      <!-- Form -->
      <form on:submit={handleSubmit} class="p-6 space-y-6">
        <div>
          <label for="name" class="block text-sm font-bold mb-2" style="color: var(--foreground);">
            Full Name *
          </label>
          <input
            id="name"
            type="text"
            bind:value={formData.name}
            required
            class="input-brutal w-full"
            placeholder="Your full name"
          />
        </div>
        
        <div>
          <label for="email" class="block text-sm font-bold mb-2" style="color: var(--foreground);">
            Email Address *
          </label>
          <input
            id="email"
            type="email"
            bind:value={formData.email}
            required
            class="input-brutal w-full"
            placeholder="<EMAIL>"
          />
        </div>
        
        <div>
          <label for="website" class="block text-sm font-bold mb-2" style="color: var(--foreground);">
            Company Website
          </label>
          <input
            id="website"
            type="url"
            bind:value={formData.website}
            class="input-brutal w-full"
            placeholder="https://yourcompany.com"
          />
        </div>
        
        <div>
          <label for="description" class="block text-sm font-bold mb-2" style="color: var(--foreground);">
            What would you like to learn or discuss? *
          </label>
          <textarea
            id="description"
            bind:value={formData.description}
            required
            rows="4"
            class="textarea-brutal w-full"
            placeholder="Tell us about your marketing challenges, goals, or what you'd like to explore with our team..."
          ></textarea>
        </div>
        
        <!-- Success/Error Messages -->
        {#if submitMessage}
          <div class="p-4 rounded-lg" style="background-color: #10b981; color: white;">
            {submitMessage}
          </div>
        {/if}
        
        {#if submitError}
          <div class="p-4 rounded-lg" style="background-color: #ef4444; color: white;">
            {submitError}
          </div>
        {/if}
        
        <div class="flex gap-4 pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            class="btn-primary flex-1 px-6 py-3 font-bold {isSubmitting ? 'opacity-75 cursor-not-allowed' : ''}"
          >
            {isSubmitting ? 'Sending...' : 'Send Message'}
          </button>
          <button
            type="button"
            on:click={closeModal}
            class="btn-secondary px-6 py-3 font-bold"
          >
            Cancel
          </button>
        </div>
      </form>
    </div>
  </div>
{/if}