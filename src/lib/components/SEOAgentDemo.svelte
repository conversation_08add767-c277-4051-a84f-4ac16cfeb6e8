<script lang="ts">
  import { writable } from 'svelte/store'
  import { 
    Target, 
    Search,
    BarChart,
    ChevronRight, 
    Bot, 
    User, 
    Clock,
    Zap 
  } from 'lucide-svelte'
  
  interface Message {
    id: string
    role: 'user' | 'assistant'
    content: string
    timestamp: Date
    metadata?: any
  }
  
  const messages = writable<Message[]>([])
  let input = ''
  let isLoading = false
  let showDemo = false
  
  const demoPrompts = [
    "Find long-tail keywords for organic skincare products",
    "Analyze local SEO keywords for coffee shops in Seattle", 
    "Research B2B keywords for project management software"
  ]
  
  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }
  
  async function sendDemoMessage(prompt?: string) {
    const messageText = prompt || input.trim()
    if (!messageText || isLoading) return
    
    input = ''
    isLoading = true
    showDemo = true
    
    // Add user message
    messages.update(msgs => [
      ...msgs,
      {
        id: generateId(),
        role: 'user',
        content: messageText,
        timestamp: new Date()
      }
    ])
    
    try {
      const response = await fetch('/api/demo/seo', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ message: messageText })
      })
      
      const data = await response.json()
      
      if (response.ok) {
        messages.update(msgs => [
          ...msgs,
          {
            id: generateId(),
            role: 'assistant',
            content: data.response,
            timestamp: new Date(),
            metadata: data.metadata
          }
        ])
      } else {
        throw new Error(data.error || 'Failed to get response')
      }
    } catch (error) {
      console.error('Demo error:', error)
      messages.update(msgs => [
        ...msgs,
        {
          id: generateId(),
          role: 'assistant',
          content: error instanceof Error && error.message.includes('rate limit') 
            ? 'You\'ve reached the demo limit (5 requests per hour). Sign up for unlimited access!'
            : 'Sorry, there was an error processing your request. Please try again.',
          timestamp: new Date()
        }
      ])
    } finally {
      isLoading = false
    }
  }
  
  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault()
      sendDemoMessage()
    }
  }
  
  function formatContent(content: string): string {
    return content
      .replace(/^# (.*$)/gim, '<h1 class="text-xl font-bold mb-3" style="color: var(--foreground);">$1</h1>')
      .replace(/^## (.*$)/gim, '<h2 class="text-lg font-bold mb-2" style="color: var(--foreground);">$1</h2>')
      .replace(/^### (.*$)/gim, '<h3 class="text-base font-bold mb-2" style="color: var(--foreground);">$1</h3>')
      .replace(/^\*\*(.*?)\*\*/gim, '<strong class="font-bold" style="color: var(--foreground);">$1</strong>')
      .replace(/^[\-\*] (.*$)/gim, '<li class="ml-4 text-sm" style="color: var(--muted-foreground);">• $1</li>')
      .replace(/\n\n/gim, '</p><p class="text-sm mb-2" style="color: var(--muted-foreground);">')
      .replace(/^(?!<[h|l|s])(.+)$/gim, '<p class="text-sm mb-2" style="color: var(--muted-foreground);">$1</p>')
      // Add table formatting for SEO data
      .replace(/\|(.+)\|/g, (match) => {
        const cells = match.split('|').filter(cell => cell.trim())
        const isHeader = cells.some(cell => cell.trim().match(/^[\-:]+$/))
        if (isHeader) return ''
        return `<tr>${cells.map(cell => `<td class="px-2 py-1 text-xs border-b border-border/50">${cell.trim()}</td>`).join('')}</tr>`
      })
  }
</script>

<!-- SEO Agent Demo Section -->
<div class="linear-card p-6 rounded-2xl border border-border/50 bg-card/80 backdrop-blur-sm">
  <div class="text-center mb-6">
    <div class="flex items-center justify-center gap-2 mb-4">
      <div class="w-8 h-8 flex items-center justify-center border-2 border-primary bg-primary rounded">
        <Target class="w-4 h-4 text-primary-foreground" />
      </div>
      <h3 class="text-lg font-bold">Try Lexi Live</h3>
    </div>
    <p class="text-sm text-muted-foreground mb-4">
      Get instant SEO keyword analysis for any topic
    </p>
  </div>
  
  {#if !showDemo}
    <!-- Demo Prompt Cards -->
    <div class="grid gap-3 mb-4">
      {#each demoPrompts as prompt}
        <button
          on:click={() => sendDemoMessage(prompt)}
          class="text-left p-3 border-2 border-border/50 rounded-lg hover:border-primary/50 hover:-translate-y-1 transition-all duration-200 group bg-background/50"
        >
          <div class="flex items-center gap-2 mb-1">
            <Search class="w-3 h-3 text-primary" />
            <span class="text-xs font-medium text-foreground">{prompt}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-xs text-muted-foreground">Click to analyze →</span>
            <ChevronRight class="w-3 h-3 text-muted-foreground group-hover:text-primary transition-colors" />
          </div>
        </button>
      {/each}
    </div>
    
    <!-- Custom Input -->
    <div class="flex gap-2">
      <input
        bind:value={input}
        on:keydown={handleKeyDown}
        placeholder="e.g., vegan protein powder, sustainable fashion..."
        class="flex-1 px-3 py-2 text-sm border-2 border-border rounded-lg focus:border-primary focus:outline-none bg-background"
        disabled={isLoading}
      />
      <button
        on:click={() => sendDemoMessage()}
        disabled={!input.trim() || isLoading}
        class="px-4 py-2 bg-primary text-primary-foreground rounded-lg text-sm font-medium hover:opacity-90 transition-opacity disabled:opacity-50"
      >
        {#if isLoading}
          <Zap class="w-3 h-3 animate-spin" />
        {:else}
          Analyze
        {/if}
      </button>
    </div>
  {:else}
    <!-- Demo Chat Interface -->
    <div class="space-y-4 max-h-96 overflow-y-auto">
      {#each $messages as message}
        <div class="flex gap-3 {message.role === 'user' ? 'flex-row-reverse' : ''}">
          <div class="w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded {message.role === 'user' ? 'bg-primary' : 'bg-secondary'}">
            {#if message.role === 'user'}
              <User class="w-3 h-3 text-primary-foreground" />
            {:else}
              <Target class="w-3 h-3 text-secondary-foreground" />
            {/if}
          </div>
          
          <div class="flex-1 max-w-md">
            <div class="flex items-center gap-2 mb-1">
              <span class="text-xs font-medium text-foreground">
                {message.role === 'user' ? 'You' : 'Lexi'}
              </span>
              <Clock class="w-2 h-2 text-muted-foreground" />
              <span class="text-xs text-muted-foreground">
                {message.timestamp.toLocaleTimeString()}
              </span>
            </div>
            
            {#if message.role === 'user'}
              <div class="p-3 bg-primary/10 border border-primary/20 rounded-lg">
                <p class="text-xs text-foreground">{message.content}</p>
              </div>
            {:else}
              <div class="p-3 bg-background border border-border rounded-lg">
                <div class="prose prose-xs max-w-none">
                  {@html formatContent(message.content)}
                </div>
                {#if message.metadata?.keywords}
                  <div class="mt-3 pt-3 border-t border-border/50">
                    <div class="flex items-center gap-2 mb-2">
                      <BarChart class="w-3 h-3 text-primary" />
                      <span class="text-xs font-medium text-foreground">Top Keywords</span>
                    </div>
                    <div class="flex flex-wrap gap-1">
                      {#each message.metadata.keywords.slice(0, 5) as keyword}
                        <span class="px-2 py-1 bg-primary/10 text-primary text-xs rounded">
                          {keyword}
                        </span>
                      {/each}
                    </div>
                  </div>
                {/if}
              </div>
            {/if}
          </div>
        </div>
      {/each}
      
      {#if isLoading}
        <div class="flex gap-3">
          <div class="w-6 h-6 flex-shrink-0 flex items-center justify-center border border-border rounded bg-secondary">
            <Target class="w-3 h-3 text-secondary-foreground" />
          </div>
          <div class="flex-1">
            <div class="p-3 bg-background border border-border rounded-lg">
              <div class="flex items-center gap-2">
                <div class="w-2 h-2 bg-primary rounded-full animate-pulse"></div>
                <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-200"></div>
                <div class="w-2 h-2 bg-primary rounded-full animate-pulse animation-delay-400"></div>
                <span class="text-xs text-muted-foreground">Researching keywords...</span>
              </div>
            </div>
          </div>
        </div>
      {/if}
    </div>
    
    <div class="mt-4 pt-4 border-t border-border/50 text-center">
      <p class="text-xs text-muted-foreground mb-2">
        This is a demo with limited capabilities
      </p>
      <button
        on:click={() => {showDemo = false; messages.set([])}}
        class="text-xs text-primary hover:underline"
      >
        Try another query
      </button>
    </div>
  {/if}
</div>