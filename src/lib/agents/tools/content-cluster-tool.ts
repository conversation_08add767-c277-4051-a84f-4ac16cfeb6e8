import { z } from 'zod'
import { createTool } from '@mastra/core'

const contentClusterSchema = z.object({
  mainTopic: z.string().describe('The main topic or seed keyword to build clusters around'),
  businessType: z.string().optional().describe('Type of business (e.g., SaaS, E-commerce, Local Business)'),
  targetAudience: z.string().optional().describe('Target audience for the content'),
  depth: z.number().optional().default(3).describe('Number of cluster levels to generate (1-5)')
})

interface ContentNode {
  id: string
  label: string
  title: string
  type: 'pillar' | 'cluster' | 'supporting'
  searchVolume: number
  difficulty: number
  keywords: string[]
  level: number
  group: number
}

interface ContentEdge {
  from: string
  to: string
  strength: number
  label?: string
}

export const contentClusterTool = createTool({
  id: 'create_content_clusters',
  description: 'Analyze a topic and create content clusters with hierarchical relationships for building topical authority',
  inputSchema: contentClusterSchema,
  execute: async (context) => {
    const { mainTopic, businessType, targetAudience, depth } = context.context
    
    console.log('Creating content clusters for:', mainTopic, 'with depth:', depth)
    
    // In a real implementation, this would use AI/API to analyze semantic relationships
    // For now, we'll create a mock cluster structure
    
    const nodes: ContentNode[] = []
    const edges: ContentEdge[] = []
    let nodeId = 0
    
    // Create pillar content node
    const pillarId = `node_${nodeId++}`
    nodes.push({
      id: pillarId,
      label: mainTopic,
      title: `Ultimate Guide to ${mainTopic}`,
      type: 'pillar',
      searchVolume: Math.floor(Math.random() * 50000) + 10000,
      difficulty: Math.floor(Math.random() * 30) + 40,
      keywords: [mainTopic, `${mainTopic} guide`, `what is ${mainTopic}`],
      level: 0,
      group: 0
    })
    
    // Generate cluster topics based on main topic
    const clusterTopics = generateClusterTopics(mainTopic)
    const clusterNodes: ContentNode[] = []
    
    // Create cluster nodes
    clusterTopics.forEach((topic, index) => {
      const clusterId = `node_${nodeId++}`
      const clusterNode: ContentNode = {
        id: clusterId,
        label: topic.name,
        title: topic.title,
        type: 'cluster',
        searchVolume: Math.floor(Math.random() * 20000) + 5000,
        difficulty: Math.floor(Math.random() * 40) + 20,
        keywords: topic.keywords,
        level: 1,
        group: index + 1
      }
      
      nodes.push(clusterNode)
      clusterNodes.push(clusterNode)
      
      // Create edge from pillar to cluster
      edges.push({
        from: pillarId,
        to: clusterId,
        strength: 0.8,
        label: 'covers'
      })
    })
    
    // Generate supporting content for each cluster
    if (depth >= 2) {
      clusterNodes.forEach((clusterNode) => {
        const supportingTopics = generateSupportingTopics(clusterNode.label, mainTopic)
        
        supportingTopics.forEach((topic, index) => {
          if (depth === 2 && index > 3) return // Limit supporting content at depth 2
          
          const supportingId = `node_${nodeId++}`
          nodes.push({
            id: supportingId,
            label: topic.name,
            title: topic.title,
            type: 'supporting',
            searchVolume: Math.floor(Math.random() * 5000) + 500,
            difficulty: Math.floor(Math.random() * 30) + 10,
            keywords: topic.keywords,
            level: 2,
            group: clusterNode.group
          })
          
          // Create edge from cluster to supporting
          edges.push({
            from: clusterNode.id,
            to: supportingId,
            strength: 0.6,
            label: 'supports'
          })
          
          // Create weak connections between related supporting content
          if (Math.random() > 0.7 && nodes.length > 5) {
            const randomNode = nodes[Math.floor(Math.random() * (nodes.length - 5)) + 5]
            if (randomNode.type === 'supporting' && randomNode.id !== supportingId) {
              edges.push({
                from: supportingId,
                to: randomNode.id,
                strength: 0.3,
                label: 'related'
              })
            }
          }
        })
      })
    }
    
    return {
      success: true,
      mainTopic,
      totalNodes: nodes.length,
      totalEdges: edges.length,
      nodes,
      edges,
      metadata: {
        businessType,
        targetAudience,
        depth,
        timestamp: new Date().toISOString()
      }
    }
  }
})

function generateClusterTopics(mainTopic: string): Array<{name: string, title: string, keywords: string[]}> {
  // Generate contextual cluster topics based on main topic
  const topicMap: Record<string, Array<{name: string, title: string, keywords: string[]}>> = {
    'project management': [
      {
        name: 'Agile Methods',
        title: 'Agile Project Management Complete Guide',
        keywords: ['agile project management', 'scrum methodology', 'sprint planning']
      },
      {
        name: 'PM Tools',
        title: 'Best Project Management Tools Comparison',
        keywords: ['project management software', 'pm tools comparison', 'best pm tools']
      },
      {
        name: 'Team Leadership',
        title: 'Project Team Leadership Strategies',
        keywords: ['project team leadership', 'managing project teams', 'team communication']
      },
      {
        name: 'Risk Management',
        title: 'Project Risk Management Framework',
        keywords: ['project risk management', 'risk assessment', 'risk mitigation']
      },
      {
        name: 'Budgeting',
        title: 'Project Budget Management Guide',
        keywords: ['project budgeting', 'cost management', 'budget tracking']
      }
    ],
    'content marketing': [
      {
        name: 'Content Strategy',
        title: 'Building a Content Marketing Strategy',
        keywords: ['content strategy', 'content planning', 'editorial calendar']
      },
      {
        name: 'SEO Content',
        title: 'SEO Content Writing Best Practices',
        keywords: ['seo content writing', 'keyword optimization', 'search intent']
      },
      {
        name: 'Content Distribution',
        title: 'Content Distribution Channels Guide',
        keywords: ['content distribution', 'content promotion', 'multi-channel marketing']
      },
      {
        name: 'Content Analytics',
        title: 'Measuring Content Marketing Success',
        keywords: ['content analytics', 'content roi', 'performance metrics']
      },
      {
        name: 'Visual Content',
        title: 'Visual Content Marketing Strategies',
        keywords: ['visual content', 'infographics', 'video marketing']
      }
    ]
  }
  
  // Default clusters for any topic
  const defaultClusters = [
    {
      name: `${mainTopic} Basics`,
      title: `Understanding ${mainTopic} Fundamentals`,
      keywords: [`${mainTopic} basics`, `${mainTopic} for beginners`, `intro to ${mainTopic}`]
    },
    {
      name: `${mainTopic} Best Practices`,
      title: `${mainTopic} Best Practices Guide`,
      keywords: [`${mainTopic} best practices`, `${mainTopic} tips`, `${mainTopic} strategies`]
    },
    {
      name: `${mainTopic} Tools`,
      title: `Essential ${mainTopic} Tools and Resources`,
      keywords: [`${mainTopic} tools`, `${mainTopic} software`, `${mainTopic} resources`]
    },
    {
      name: `${mainTopic} Trends`,
      title: `Latest ${mainTopic} Trends and Future`,
      keywords: [`${mainTopic} trends`, `future of ${mainTopic}`, `${mainTopic} innovations`]
    }
  ]
  
  // Check if we have specific clusters for this topic
  const normalizedTopic = mainTopic.toLowerCase()
  for (const [key, clusters] of Object.entries(topicMap)) {
    if (normalizedTopic.includes(key) || key.includes(normalizedTopic)) {
      return clusters
    }
  }
  
  return defaultClusters
}

function generateSupportingTopics(clusterTopic: string, mainTopic: string): Array<{name: string, title: string, keywords: string[]}> {
  // Generate supporting content ideas based on cluster topic
  const templates = [
    {
      name: `How to ${clusterTopic}`,
      title: `Step-by-Step Guide: How to ${clusterTopic}`,
      keywords: [`how to ${clusterTopic}`, `${clusterTopic} tutorial`, `${clusterTopic} guide`]
    },
    {
      name: `${clusterTopic} Examples`,
      title: `Real-World ${clusterTopic} Examples and Case Studies`,
      keywords: [`${clusterTopic} examples`, `${clusterTopic} case studies`, `${clusterTopic} success stories`]
    },
    {
      name: `${clusterTopic} Mistakes`,
      title: `Common ${clusterTopic} Mistakes to Avoid`,
      keywords: [`${clusterTopic} mistakes`, `${clusterTopic} errors`, `${clusterTopic} pitfalls`]
    },
    {
      name: `${clusterTopic} Checklist`,
      title: `Complete ${clusterTopic} Checklist and Templates`,
      keywords: [`${clusterTopic} checklist`, `${clusterTopic} template`, `${clusterTopic} worksheet`]
    },
    {
      name: `${clusterTopic} vs Alternatives`,
      title: `${clusterTopic} vs Other ${mainTopic} Approaches`,
      keywords: [`${clusterTopic} comparison`, `${clusterTopic} alternatives`, `${clusterTopic} vs`]
    }
  ]
  
  return templates
}