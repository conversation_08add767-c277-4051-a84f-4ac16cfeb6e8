import { z } from 'zod'
import { createTool } from '@mastra/core'
import { env } from '$env/dynamic/private'

const keywordsForSiteSchema = z.object({
  target: z.string().describe('Domain or page URL to get keywords for (e.g., example.com or example.com/page)'),
  location_code: z.number().optional().default(2840).describe('Location code for search data (default: 2840 for United States)'),
  language_code: z.string().optional().default('en').describe('Language code for search data (default: en for English)'),
  search_partners: z.boolean().optional().default(false).describe('Include Google search partners data'),
  exclude_adult_keywords: z.boolean().optional().default(true).describe('Exclude adult keywords from results')
})

export const keywordsForSiteTool = createTool({
  id: 'get_keywords_for_site',
  description: 'Get all keywords a website or webpage ranks for using DataForSEO Keywords for Site API',
  inputSchema: keywordsForSiteSchema,
  execute: async (context) => {
    const { target, location_code, language_code, search_partners, exclude_adult_keywords } = context.context
    
    const apiLogin = env.DATAFORSEO_LOGIN
    const apiPassword = env.DATAFORSEO_PASSWORD
    
    console.log('Keywords for Site Tool - Checking credentials:', { 
      hasLogin: !!apiLogin, 
      hasPassword: !!apiPassword,
      target 
    })
    
    if (!apiLogin || !apiPassword) {
      console.error('DataForSEO credentials missing - returning mock data for testing')
      // Return mock data for testing
      const mockResults = []
      const mockKeywords = [
        'best project management tools', 'free task management software', 
        'team collaboration platforms', 'agile project management',
        'scrum software comparison', 'kanban board tools',
        'time tracking for teams', 'resource planning software',
        'gantt chart maker online', 'project management for small teams',
        'enterprise project management', 'cloud based project tools'
      ]
      
      for (const keyword of mockKeywords) {
        mockResults.push({
          keyword,
          search_volume: Math.floor(Math.random() * 30000) + 500,
          competition: ['LOW', 'MEDIUM', 'HIGH'][Math.floor(Math.random() * 3)],
          competition_index: Math.floor(Math.random() * 100),
          cpc: Math.random() * 8 + 0.3,
          monthly_searches: Array(12).fill(0).map(() => ({
            year: 2024,
            month: Math.floor(Math.random() * 12) + 1,
            search_volume: Math.floor(Math.random() * 5000) + 100
          }))
        })
      }
      
      // Sort by search volume
      mockResults.sort((a, b) => b.search_volume - a.search_volume)
      
      return {
        success: true,
        target,
        total_keywords: mockResults.length,
        results: mockResults,
        note: 'Mock data returned - DataForSEO credentials not configured'
      }
    }

    // Create base64 encoded credentials
    const credentials = Buffer.from(`${apiLogin}:${apiPassword}`).toString('base64')
    
    try {
      const requestData = [{
        target,
        location_code,
        language_code,
        search_partners,
        exclude_adult_keywords
      }]

      console.log('Making DataForSEO keywords for site request for:', target)
      
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 30000) // 30 second timeout
      
      const response = await fetch('https://api.dataforseo.com/v3/keywords_data/google_ads/keywords_for_site/live', {
        method: 'POST',
        headers: {
          'Authorization': `Basic ${credentials}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(requestData),
        signal: controller.signal
      })
      
      clearTimeout(timeoutId)

      if (!response.ok) {
        throw new Error(`DataForSEO API error: ${response.status} ${response.statusText}`)
      }

      const data = await response.json()
      console.log('DataForSEO keywords for site response status:', data.status_code, data.status_message)
      
      if (data.status_code !== 20000) {
        console.error('DataForSEO API error response:', data)
        throw new Error(`DataForSEO API error: ${data.status_message}`)
      }

      // Extract and format the results
      const results = []
      if (data.tasks && data.tasks[0] && data.tasks[0].result) {
        console.log('Processing keywords for site results')
        for (const item of data.tasks[0].result) {
          results.push({
            keyword: item.keyword,
            search_volume: item.search_volume || 0,
            competition: item.competition || 'UNKNOWN',
            competition_index: item.competition_index || 0,
            cpc: item.cpc || 0,
            monthly_searches: item.monthly_searches || [],
            keyword_annotations: item.keyword_annotations || {}
          })
        }
      } else {
        console.warn('No keywords for site results found in response')
      }

      // Sort by search volume descending
      results.sort((a, b) => b.search_volume - a.search_volume)

      console.log('Keywords for site tool completed successfully, returning', results.length, 'results')
      return {
        success: true,
        target,
        total_keywords: results.length,
        results,
        cost: data.cost || 0
      }
    } catch (error) {
      console.error('Keywords for site API error:', error)
      throw new Error(`Failed to get keywords for site data: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})