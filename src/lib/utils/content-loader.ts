import matter from 'gray-matter';
import { marked } from 'marked';
import { dev } from '$app/environment';
import type { 
  BaseContent, 
  ContentLoaderResult, 
  ContentCollectionResult, 
  ContentPath 
} from '$lib/types/content';

// Configure marked for better HTML output
marked.setOptions({
  breaks: true,
  gfm: true,
});

/**
 * Load a single markdown file with frontmatter
 */
export async function loadMarkdownContent<T extends BaseContent>(
  path: string
): Promise<ContentLoaderResult<T>> {
  try {
    // Import all markdown files statically
    const modules = import.meta.glob('/src/content/**/*.md', {
      as: 'raw',
      eager: true
    });

    const fullPath = `/src/content/${path}.md`;
    const rawContent = modules[fullPath];

    if (!rawContent) {
      return {
        success: false,
        error: `Content not found: ${path}`
      };
    }

    // Content is already loaded as string in eager mode
    const { data: frontmatter, content: rawMarkdown } = matter(rawContent as string);
    const htmlContent = await marked(rawMarkdown);

    return {
      success: true,
      data: {
        frontmatter,
        content: htmlContent,
        slug: path.split('/').pop()
      } as T
    };
  } catch (error) {
    return {
      success: false,
      error: `Error loading content: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Load all markdown files from a directory
 */
export async function loadContentCollection<T extends BaseContent>(
  directory: ContentPath
): Promise<ContentCollectionResult<T>> {
  try {
    // Import all markdown files statically
    const modules = import.meta.glob('/src/content/**/*.md', {
      as: 'raw',
      eager: true
    });

    const items: T[] = [];
    
    for (const [path, rawContent] of Object.entries(modules)) {
      if (path.includes(`/src/content/${directory}/`)) {
        // Content is already loaded as string in eager mode
        const { data: frontmatter, content: rawMarkdown } = matter(rawContent as string);
        const htmlContent = await marked(rawMarkdown);
        const slug = path.split('/').pop()?.replace('.md', '');

        items.push({
          frontmatter,
          content: htmlContent,
          slug
        } as T);
      }
    }

    // Sort by order if available, otherwise by slug
    const sortedItems = items.sort((a, b) => {
      const orderA = a.frontmatter.order || 0;
      const orderB = b.frontmatter.order || 0;
      
      if (orderA !== orderB) {
        return orderA - orderB;
      }
      
      return (a.slug || '').localeCompare(b.slug || '');
    });

    return {
      success: true,
      data: sortedItems
    };
  } catch (error) {
    return {
      success: false,
      error: `Error loading content collection: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
}

/**
 * Load multiple content files at once
 */
export async function loadMultipleContent<T extends BaseContent>(
  paths: string[]
): Promise<ContentLoaderResult<T>[]> {
  const promises = paths.map(path => loadMarkdownContent<T>(path));
  return Promise.all(promises);
}

/**
 * Get content file paths for a specific directory
 */
export function getContentPaths(directory: ContentPath): string[] {
  // Import all markdown files statically
  const modules = import.meta.glob('/src/content/**/*.md', {
    as: 'raw',
    eager: true
  });

  return Object.keys(modules)
    .filter(path => path.includes(`/src/content/${directory}/`))
    .map(path => path.replace('/src/content/', '').replace('.md', ''));
}

/**
 * Validate content structure
 */
export function validateContent<T extends BaseContent>(
  content: T,
  requiredFields: string[]
): { valid: boolean; missing: string[] } {
  const missing: string[] = [];
  
  for (const field of requiredFields) {
    if (!(field in content.frontmatter)) {
      missing.push(field);
    }
  }

  return {
    valid: missing.length === 0,
    missing
  };
}

/**
 * Helper to safely access nested frontmatter properties
 */
export function getFrontmatterValue<T>(
  frontmatter: Record<string, any>,
  key: string,
  defaultValue: T
): T {
  return frontmatter[key] ?? defaultValue;
}

/**
 * Simple in-memory cache for content (optional, since we're using eager loading)
 */
const contentCache = new Map<string, any>();

export function getCachedContent<T>(key: string): T | null {
  return contentCache.get(key) || null;
}

export function setCachedContent<T>(key: string, content: T): void {
  contentCache.set(key, content);
}