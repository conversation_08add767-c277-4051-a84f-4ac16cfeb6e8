<script lang="ts">
  import { Menu } from "lucide-svelte"
  import { But<PERSON> } from "$lib/components/ui/button"
  import * as DropDownMenu from "$lib/components/ui/dropdown-menu"
  import { getEnvironmentState } from "$lib/states/environment.svelte"
  import SharedFooter from "$lib/components/SharedFooter.svelte"
  import { page } from "$app/stores"

  import { WebsiteName } from "$lib/config"

  let { data, children } = $props()
  const environment = getEnvironmentState()
  
  // Check if we're on a login-related page
  const isLoginPage = $derived(
    $page.url.pathname.startsWith('/login') || 
    $page.url.pathname.startsWith('/sign_up') ||
    $page.url.pathname.startsWith('/forgot_password') ||
    $page.url.pathname.startsWith('/check_email')
  )
</script>

<div class="bg-background border-b-2 border-border sticky top-0 z-50 nav-blur">
  <div class="max-w-6xl mx-auto px-6 py-4">
    <div class="flex items-center justify-between">
      <div class="flex-1 flex items-center space-x-2">
        <div
          class="w-8 h-8 flex items-center justify-center bg-primary text-primary-foreground border-2 border-border shadow-brutal-sm"
        >
          <span class="font-bold text-sm">R</span>
        </div>
        <a
          href="/"
          class="text-xl font-bold text-foreground hover:opacity-70 transition-colors"
        >
          {WebsiteName}
        </a>
      </div>
      {#if !isLoginPage}
        <div class="flex-none">
          <ul class="hidden sm:flex items-center gap-8 font-bold">
            <li>
              <a
                href="/pricing"
                class="text-muted-foreground hover:text-foreground transition-colors"
              >
                Pricing
              </a>
            </li>
            {#if !data.auth.user || data.auth.user?.is_anonymous}
              <li>
                <a
                  href="/login"
                  class="text-muted-foreground hover:text-foreground transition-colors"
                >
                  Sign In
                </a>
              </li>
            {:else}
              <li>
                <a
                  href="/sign_out"
                  class="text-muted-foreground hover:text-foreground transition-colors"
                >
                  Sign Out
                </a>
              </li>
            {/if}
            <li>
              {#if !environment.value}
                <a href="/onboarding" class="btn-primary px-6 py-2">
                  Get Started
                </a>
              {:else}
                <a
                  href="/dashboard/{environment.value.slug}"
                  class="btn-primary px-6 py-2"
                >
                  Dashboard
                </a>
              {/if}
            </li>
          </ul>

          <div class="sm:hidden">
            <DropDownMenu.Root>
              <DropDownMenu.Trigger asChild let:builder>
                <Button
                  builders={[builder]}
                  variant="ghost"
                  size="sm"
                  class="text-muted-foreground"
                >
                  <Menu class="h-5 w-5" />
                </Button>
              </DropDownMenu.Trigger>
              <DropDownMenu.Content class="w-56 sm:hidden">
                <DropDownMenu.Item>
                  <a href="/pricing" class="w-full">Pricing</a>
                </DropDownMenu.Item>
                {#if !data.auth.user || data.auth.user?.is_anonymous}
                  <DropDownMenu.Item>
                    <a href="/login" class="w-full">Sign In</a>
                  </DropDownMenu.Item>
                {:else}
                  <DropDownMenu.Item>
                    <a href="/sign_out" class="w-full">Sign Out</a>
                  </DropDownMenu.Item>
                {/if}
                <DropDownMenu.Item>
                  {#if !environment.value}
                    <a href="/onboarding" class="w-full">Get Started</a>
                  {:else}
                    <a href="/dashboard/{environment.value.slug}" class="w-full">
                      Dashboard
                    </a>
                  {/if}
                </DropDownMenu.Item>
              </DropDownMenu.Content>
            </DropDownMenu.Root>
          </div>
        </div>
      {/if}
    </div>
  </div>
</div>

<main class="flex-1">
  {@render children()}
</main>

<SharedFooter />
