<script lang="ts">
  import { Auth } from "@supabase/auth-ui-svelte"
  import { sharedAppearance, oauthProviders } from "../login_config"
  import { goto } from "$app/navigation"
  import { onMount } from "svelte"
  import { invalidate } from "$app/navigation"
  import { page } from "$app/stores"

  let { data } = $props()
  let { supabase } = data

  onMount(() => {
    supabase.auth.onAuthStateChange(async (event, session) => {
      // Redirect to account after successful login
      if (event == "SIGNED_IN") {
        await invalidate("data:init")

        // Delay needed because order of callback not guaranteed.
        // Give the layout callback priority to update state or
        // we'll just bounch back to login when /account tries to load
        if (session?.user.is_anonymous) {
          return
        }
        setTimeout(async () => {
          goto("/find-env")
        }, 1)
      }
    })
  })
</script>

<svelte:head>
  <title>Sign in</title>
</svelte:head>

{#if $page.url.searchParams.get("verified") == "true"}
  <div role="alert" class="alert alert-success mb-5">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="stroke-current shrink-0 h-6 w-6"
      fill="none"
      viewBox="0 0 24 24"
      ><path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
      /></svg
    >
    <span>Email verified! Please sign in.</span>
  </div>
{/if}

{#if $page.url.searchParams.get("error")}
  <div role="alert" class="alert alert-error mb-5">
    <svg
      xmlns="http://www.w3.org/2000/svg"
      class="stroke-current shrink-0 h-6 w-6"
      fill="none"
      viewBox="0 0 24 24"
      ><path
        stroke-linecap="round"
        stroke-linejoin="round"
        stroke-width="2"
        d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"
      /></svg
    >
    <span>
      {#if $page.url.searchParams.get("error") === "auth_failed"}
        Authentication failed. Please try again.
      {:else if $page.url.searchParams.get("error") === "session_failed"}
        Session creation failed. Please try signing in again.
      {:else if $page.url.searchParams.get("error") === "email_not_confirmed"}
        Please confirm your email before signing in. Check your inbox for the
        confirmation link.
      {:else}
        An unexpected error occurred. Please try again.
      {/if}
    </span>
  </div>
{/if}
<h1 class="text-2xl font-bold mb-6">Sign In</h1>
<Auth
  supabaseClient={data.supabase}
  view="sign_in"
  redirectTo={`${data.url}/auth/callback`}
  providers={oauthProviders}
  socialLayout="horizontal"
  showLinks={false}
  appearance={sharedAppearance}
  additionalData={undefined}
/>
<div class="text-l text-primary mt-4">
  <a class="underline" href="/login/forgot_password">Forgot password?</a>
</div>
<div class="text-l text-primary mt-3">
  Don't have an account? <a class="underline" href="/login/sign_up">Sign up</a>.
</div>
