<script lang="ts">
  import { writable } from "svelte/store"
  import { page } from "$app/stores"
  import {
    Search,
    Download,
    Target,
    TrendingUp,
    FileText,
    Clock,
    User,
    Bot,
    ChevronRight,
    Sparkles,
    Filter,
    Copy,
    FileDown,
    BarChart,
    BookOpen,
    Lightbulb,
    CheckCircle2,
    Loader2,
    Circle,
    Share2,
  } from "lucide-svelte"
  import { onMount } from "svelte"
  import { slide, fade } from "svelte/transition"
  import NicheDiscovery from "./NicheDiscovery.svelte"
  import CompetitorGapAnalysis from "./CompetitorGapAnalysis.svelte"
  import ContentClusterMapping from "./ContentClusterMapping.svelte"

  interface Message {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: Date
    isReport?: boolean
  }

  interface ProgressStep {
    id: number
    title: string
    description: string
    status: "pending" | "active" | "completed"
    progress?: number
  }

  const messages = writable<Message[]>([])
  let input = ""
  let isLoading = false
  let selectedMessageId = ""
  let outputFormat = "summary"
  let placeholderIndex = 0
  let currentPlaceholder = ""
  let showFilters = false
  let targetAudience = ""
  let regionFocus = ""
  let funnelStage = "awareness"
  let progressSteps: ProgressStep[] = []
  let currentProgress = 0
  let mode: "chat" | "niche" | "gap" | "cluster" = "chat"
  let nicheKeywords: Array<{
    keyword: string
    search_volume: number
    difficulty: number
    competition: string
    cpc: number
    opportunity_score?: number
  }> = []
  let gapKeywords: Array<{
    keyword: string
    search_volume: number
    difficulty: number
    competition: string
    cpc: number
    competitor_position: number
    your_position: number | null
    gap_type: 'missing' | 'lower_rank' | 'opportunity'
    opportunity_score?: number
  }> = []
  let gapProgressSteps: ProgressStep[] = []
  let gapCurrentProgress = 0
  
  // Content cluster state
  let contentNodes: Array<{
    id: string
    label: string
    title: string
    type: 'pillar' | 'cluster' | 'supporting'
    searchVolume: number
    difficulty: number
    keywords: string[]
    level: number
    group: number
  }> = []
  let contentEdges: Array<{
    from: string
    to: string
    strength: number
    label?: string
  }> = []
  let clusterProgressSteps: ProgressStep[] = []
  let clusterCurrentProgress = 0

  // Animated placeholder examples
  const placeholderExamples = [
    "Find long-tail keywords for organic skincare...",
    "Analyze competitor keywords for project management tools...",
    "Discover niche keywords for sustainable fashion brands...",
    "Research local SEO keywords for coffee shops in Seattle...",
  ]

  // Interactive card templates
  const interactiveCards = [
    {
      icon: Search,
      title: "Niche Keyword Discovery",
      description: "Find untapped long-tail keywords in your specific niche",
      prompt:
        "Discover high-value, low-competition keywords for a [Your Niche] business targeting [Your Audience]",
    },
    {
      icon: TrendingUp,
      title: "Competitor Gap Analysis",
      description:
        "Identify keyword opportunities your competitors are missing",
      prompt:
        "Analyze keyword gaps between [Your Business] and competitors like [Competitor Names] in the [Industry] space",
    },
    {
      icon: BarChart,
      title: "Content Cluster Mapping",
      description: "Build topical authority with strategic content clusters",
      prompt:
        "Create a content cluster strategy around [Main Topic] for a [Business Type] targeting [Goal]",
    },
  ]

  function generateId(): string {
    return Math.random().toString(36).substr(2, 9)
  }

  async function sendMessage() {
    if (!input.trim() || isLoading) return

    // Build enhanced message with filters and format
    let enhancedMessage = input.trim()
    const filters = []
    if (targetAudience) filters.push(`Target audience: ${targetAudience}`)
    if (regionFocus) filters.push(`Region focus: ${regionFocus}`)
    if (funnelStage) filters.push(`Funnel stage: ${funnelStage}`)
    if (filters.length > 0) {
      enhancedMessage = `${enhancedMessage}\n\nAdditional context: ${filters.join(", ")}`
    }
    enhancedMessage += `\n\nOutput format: ${outputFormat}`

    const userMessage = enhancedMessage
    const messageId = generateId()
    input = ""
    isLoading = true

    // Initialize progress steps
    progressSteps = [
      {
        id: 1,
        title: "Industry Research",
        description: "Analyzing your business niche...",
        status: "pending",
      },
      {
        id: 2,
        title: "Keyword Discovery",
        description: "Finding relevant keywords...",
        status: "pending",
      },
      {
        id: 3,
        title: "Volume Analysis",
        description: "Checking search volumes...",
        status: "pending",
      },
      {
        id: 4,
        title: "Competition Analysis",
        description: "Analyzing keyword difficulty...",
        status: "pending",
      },
      {
        id: 5,
        title: "Report Generation",
        description: "Creating your SEO strategy...",
        status: "pending",
      },
    ]
    currentProgress = 0

    // Add user message to chat
    messages.update((msgs) => [
      ...msgs,
      {
        id: generateId(),
        role: "user",
        content: userMessage,
        timestamp: new Date(),
      },
    ])

    try {
      // Use streaming endpoint
      const response = await fetch(
        `/dashboard/${$page.params.envSlug}/agent-seo?stream=true`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ message: userMessage }),
        },
      )

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Handle streaming response
      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) {
        throw new Error("No response body")
      }

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split("\n")

        for (const line of lines) {
          if (line.startsWith("data: ")) {
            try {
              const data = JSON.parse(line.slice(6))

              if (data.type === "final") {
                // Add assistant response to chat
                const assistantMessageId = generateId()
                messages.update((msgs) => [
                  ...msgs,
                  {
                    id: assistantMessageId,
                    role: "assistant",
                    content: data.response,
                    timestamp: new Date(),
                    isReport: true,
                  },
                ])
                selectedMessageId = assistantMessageId
                
                // Parse results based on current mode
                if (mode === "gap") {
                  // Parse gap analysis results
                  const gapResults = parseGapAnalysisResults(data.response)
                  if (gapResults.length > 0) {
                    gapKeywords = gapResults
                  }
                } else if (mode === "cluster") {
                  // Parse content cluster results
                  const clusterResults = parseClusterResults(data.response)
                  if (clusterResults.nodes.length > 0) {
                    // Force reactivity by creating new arrays
                    contentNodes = [...clusterResults.nodes]
                    contentEdges = [...clusterResults.edges]
                    console.log('Cluster results loaded:', contentNodes.length, 'nodes,', contentEdges.length, 'edges')
                  }
                }
              } else if (data.type === "error") {
                throw new Error(data.error)
              } else if (data.step) {
                // Update progress
                currentProgress = data.progress
                progressSteps = progressSteps.map((step) => {
                  if (step.id === data.step) {
                    return {
                      ...step,
                      status: data.status,
                      description: data.action,
                    }
                  } else if (step.id < data.step) {
                    return { ...step, status: "completed" }
                  }
                  return step
                })
                
                // Also update gap-specific progress if in gap mode
                if (mode === "gap") {
                  gapCurrentProgress = data.progress
                  gapProgressSteps = gapProgressSteps.map((step) => {
                    if (step.id === data.step) {
                      return {
                        ...step,
                        status: data.status,
                        description: data.action,
                      }
                    } else if (step.id < data.step) {
                      return { ...step, status: "completed" }
                    }
                    return step
                  })
                }
                
                // Update cluster-specific progress if in cluster mode
                if (mode === "cluster") {
                  clusterCurrentProgress = data.progress
                  clusterProgressSteps = clusterProgressSteps.map((step) => {
                    if (step.id === data.step) {
                      return {
                        ...step,
                        status: data.status,
                        description: data.action,
                      }
                    } else if (step.id < data.step) {
                      return { ...step, status: "completed" }
                    }
                    return step
                  })
                }
              }
            } catch (e) {
              console.error("Error parsing SSE data:", e)
            }
          }
        }
      }
    } catch (error) {
      console.error("Error sending message:", error)
      messages.update((msgs) => [
        ...msgs,
        {
          id: generateId(),
          role: "assistant",
          content:
            "I apologize, but an error occurred while processing your request. Please try again.",
          timestamp: new Date(),
        },
      ])
    } finally {
      isLoading = false
      progressSteps = []
    }
  }

  function handleKeyDown(event: KeyboardEvent) {
    if (event.key === "Enter" && !event.shiftKey) {
      event.preventDefault()
      sendMessage()
    }
  }

  function downloadAsMarkdown(message: Message) {
    const businessName = extractBusinessName(message.content)
    const timestamp = message.timestamp.toISOString().split("T")[0]
    const filename = `${businessName || "seo-strategy"}-${timestamp}.md`

    const markdownContent = `# SEO Strategy Report
**Generated on:** ${message.timestamp.toLocaleDateString()}
**Time:** ${message.timestamp.toLocaleTimeString()}

---

${message.content}

---

*Report generated by Robynn.ai SEO Strategist Agent*
`

    const blob = new Blob([markdownContent], { type: "text/markdown" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = filename
    a.click()
    URL.revokeObjectURL(url)
  }

  function extractBusinessName(content: string): string {
    // Simple extraction - looks for business name in the first line or headers
    const lines = content.split("\n")
    for (const line of lines.slice(0, 5)) {
      if (line.includes("Business:") || line.includes("Company:")) {
        return (
          line
            .split(":")[1]
            ?.trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
      if (
        line.startsWith("# ") &&
        !line.includes("SEO") &&
        !line.includes("Strategy")
      ) {
        return (
          line
            .replace("# ", "")
            .trim()
            .replace(/[^a-zA-Z0-9-]/g, "") || ""
        )
      }
    }
    return ""
  }

  function formatContent(content: string): string {
    // Convert markdown-like formatting to HTML for better display
    let formatted = content
      // Headers
      .replace(
        /^### (.*$)/gim,
        '<h3 class="text-lg font-bold text-foreground mb-2 mt-4">$1</h3>',
      )
      .replace(
        /^## (.*$)/gim,
        '<h2 class="text-xl font-bold text-foreground mb-3 mt-6">$1</h2>',
      )
      .replace(
        /^# (.*$)/gim,
        '<h1 class="text-2xl font-bold text-foreground mb-4">$1</h1>',
      )
      // Bold text
      .replace(
        /\*\*(.*?)\*\*/g,
        '<strong class="font-bold text-foreground">$1</strong>',
      )
      // Bullet points with proper handling
      .replace(
        /^[\-\*•] (.*$)/gim,
        '<li class="ml-6 mb-1 text-muted-foreground list-disc">$1</li>',
      )
      // Numbered lists
      .replace(
        /^\d+\. (.*$)/gim,
        '<li class="ml-6 mb-1 text-muted-foreground list-decimal">$1</li>',
      )
      // Code blocks
      .replace(
        /```([\s\S]*?)```/g,
        '<pre class="bg-muted p-3 rounded my-3 overflow-x-auto"><code class="text-sm">$1</code></pre>',
      )
      // Inline code
      .replace(
        /`([^`]+)`/g,
        '<code class="bg-muted px-1 py-0.5 rounded text-sm">$1</code>',
      )
      // Tables
      .replace(/\|(.+)\|/g, (match) => {
        const cells = match.split("|").filter((cell) => cell.trim())
        const isHeader = cells.some((cell) => cell.trim().match(/^[\-:]+$/))
        if (isHeader) return ""
        return `<tr>${cells.map((cell) => `<td class="border border-border px-3 py-1">${cell.trim()}</td>`).join("")}</tr>`
      })

    // Wrap consecutive list items in ul/ol tags
    formatted = formatted.replace(
      /(<li class="[^"]*list-disc[^"]*">[\s\S]*?<\/li>\s*)+/g,
      '<ul class="mb-4">$&</ul>',
    )
    formatted = formatted.replace(
      /(<li class="[^"]*list-decimal[^"]*">[\s\S]*?<\/li>\s*)+/g,
      '<ol class="mb-4">$&</ol>',
    )

    // Wrap table rows in table
    formatted = formatted.replace(
      /(<tr>[\s\S]*?<\/tr>\s*)+/g,
      '<table class="w-full mb-4 border-collapse">$&</table>',
    )

    // Paragraphs - wrap lines that aren't already wrapped
    const lines = formatted.split("\n")
    formatted = lines
      .map((line) => {
        if (line.trim() && !line.startsWith("<")) {
          return `<p class="text-muted-foreground leading-relaxed mb-3">${line}</p>`
        }
        return line
      })
      .join("\n")

    return formatted
  }

  // Auto-scroll to bottom when new messages are added
  $: if ($messages.length > 0) {
    setTimeout(() => {
      const messagesContainer = document.querySelector(".messages-container")
      if (messagesContainer) {
        messagesContainer.scrollTop = messagesContainer.scrollHeight
      }
    }, 100)
  }

  // Animated placeholder rotation
  onMount(() => {
    currentPlaceholder = placeholderExamples[0]
    const interval = setInterval(() => {
      placeholderIndex = (placeholderIndex + 1) % placeholderExamples.length
      currentPlaceholder = placeholderExamples[placeholderIndex]
    }, 3000)

    return () => clearInterval(interval)
  })

  function copyToClipboard(text: string) {
    navigator.clipboard.writeText(text)
  }

  function downloadAsCSV(content: string) {
    const blob = new Blob([content], { type: "text/csv" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `seo-keywords-${new Date().toISOString().split("T")[0]}.csv`
    a.click()
    URL.revokeObjectURL(url)
  }

  function parseGapAnalysisResults(response: string): typeof gapKeywords {
    // Mock parser - in real implementation, would parse AI response
    return [
      {
        keyword: "project management software",
        search_volume: 12000,
        difficulty: 45,
        competition: "medium",
        cpc: 8.50,
        competitor_position: 3,
        your_position: null,
        gap_type: "missing",
        opportunity_score: 85
      },
      {
        keyword: "best project management tools",
        search_volume: 8500,
        difficulty: 60,
        competition: "high",
        cpc: 12.30,
        competitor_position: 5,
        your_position: 15,
        gap_type: "lower_rank",
        opportunity_score: 72
      }
    ]
  }
  
  function parseClusterResults(response: string): { nodes: typeof contentNodes, edges: typeof contentEdges } {
    // Use the topic from response or default to "Project Management"
    const topic = response || "Project Management"
    
    // Mock parser - in real implementation, would parse AI response
    const mockNodes: typeof contentNodes = [
      {
        id: "node_0",
        label: topic,
        title: `Ultimate Guide to ${topic}`,
        type: "pillar",
        searchVolume: 25000,
        difficulty: 55,
        keywords: [topic.toLowerCase(), `${topic.toLowerCase()} guide`],
        level: 0,
        group: 0
      },
      {
        id: "node_1",
        label: "Agile Methods",
        title: "Agile Project Management Complete Guide",
        type: "cluster",
        searchVolume: 12000,
        difficulty: 45,
        keywords: ["agile project management", "scrum methodology"],
        level: 1,
        group: 1
      },
      {
        id: "node_2",
        label: "PM Tools",
        title: "Best Project Management Tools Comparison",
        type: "cluster",
        searchVolume: 8500,
        difficulty: 40,
        keywords: ["project management software", "pm tools"],
        level: 1,
        group: 2
      },
      {
        id: "node_3",
        label: "How to Agile",
        title: "Step-by-Step Guide: How to Agile Methods",
        type: "supporting",
        searchVolume: 3500,
        difficulty: 25,
        keywords: ["how to agile", "agile tutorial"],
        level: 2,
        group: 1
      },
      {
        id: "node_4",
        label: "Agile Examples",
        title: "Real-World Agile Methods Examples",
        type: "supporting",
        searchVolume: 2800,
        difficulty: 20,
        keywords: ["agile examples", "agile case studies"],
        level: 2,
        group: 1
      },
      {
        id: "node_5",
        label: "Tool Comparison",
        title: "PM Tools vs Alternatives",
        type: "supporting",
        searchVolume: 1500,
        difficulty: 30,
        keywords: ["pm tools comparison", "project management alternatives"],
        level: 2,
        group: 2
      }
    ]
    
    const mockEdges: typeof contentEdges = [
      {
        from: "node_0",
        to: "node_1",
        strength: 0.8,
        label: "covers"
      },
      {
        from: "node_0",
        to: "node_2",
        strength: 0.8,
        label: "covers"
      },
      {
        from: "node_1",
        to: "node_3",
        strength: 0.6,
        label: "supports"
      },
      {
        from: "node_1",
        to: "node_4",
        strength: 0.6,
        label: "supports"
      },
      {
        from: "node_2",
        to: "node_5",
        strength: 0.6,
        label: "supports"
      },
      {
        from: "node_3",
        to: "node_4",
        strength: 0.3,
        label: "related"
      }
    ]
    
    return { nodes: mockNodes, edges: mockEdges }
  }
  
  function generateFollowUp(suggestion: string, originalContent: string) {
    let followUpPrompt = ""
    switch (suggestion) {
      case "cluster":
        followUpPrompt =
          "Cluster these keywords by search intent and create content groups"
        break
      case "blog":
        followUpPrompt =
          "Generate a blog content outline using the top 10 keywords from this analysis"
        break
      case "competition":
        followUpPrompt =
          "Analyze the competition level and ranking difficulty for each keyword group"
        break
    }
    input = followUpPrompt
    sendMessage()
  }
  
  async function handleNicheDiscovery(seedKeywords: string[], filters: { 
    industry: string
    location: string
    volumeRange: { min: number; max: number }
    maxDifficulty: number 
  }) {
    // Format the message for niche discovery
    const nicheMessage = `Discover niche keywords for: ${seedKeywords.join(", ")}. 
    Focus on long-tail variations with low competition. 
    ${filters.industry ? `Industry: ${filters.industry}. ` : ""}
    Location: ${filters.location}. 
    Volume range: ${filters.volumeRange.min}-${filters.volumeRange.max}. 
    Max difficulty: ${filters.maxDifficulty}.
    Output format: table`
    
    // Send the message
    input = nicheMessage
    await sendMessage()
    
    // Parse keywords from response (this would need to be implemented based on actual response format)
    // For now, just store mock data
    nicheKeywords = []
  }
  
  async function handleClusterMapping(filters: {
    mainTopic: string
    businessType: string
    targetAudience: string
    depth: number
  }) {
    // Initialize cluster progress steps
    clusterProgressSteps = [
      {
        id: 1,
        title: "Topic Analysis",
        description: "Analyzing main topic and semantic relationships...",
        status: "pending",
      },
      {
        id: 2,
        title: "Generate Clusters",
        description: "Creating content clusters...",
        status: "pending",
      },
      {
        id: 3,
        title: "Supporting Content",
        description: "Identifying supporting content...",
        status: "pending",
      },
      {
        id: 4,
        title: "Build Relationships",
        description: "Building content hierarchy...",
        status: "pending",
      },
      {
        id: 5,
        title: "Visualization Prep",
        description: "Preparing cluster visualization...",
        status: "pending",
      },
    ]
    clusterCurrentProgress = 0
    
    // Format the message for content cluster mapping
    const clusterMessage = `Create content clusters for: ${filters.mainTopic}.
    ${filters.businessType ? `Business type: ${filters.businessType}.` : ''}
    ${filters.targetAudience ? `Target audience: ${filters.targetAudience}.` : ''}
    Build a comprehensive content hierarchy with ${filters.depth} levels of depth.
    Output format: cluster`
    
    input = clusterMessage
    // Stay in cluster mode to show the visualization
    sendMessage()
    
    // For now, immediately load mock data for visualization
    setTimeout(() => {
      const clusterResults = parseClusterResults(filters.mainTopic)
      contentNodes = [...clusterResults.nodes]
      contentEdges = [...clusterResults.edges]
      console.log('Mock cluster data loaded for visualization')
    }, 5000) // After progress completes
  }
  
  async function handleGapAnalysis(filters: {
    yourDomain: string
    competitors: string[]
    location: string
    minVolume: number
    maxDifficulty: number
    gapType: 'missing' | 'lower_rank' | 'all'
  }) {
    // Initialize progress steps for gap analysis
    gapProgressSteps = [
      {
        id: 1,
        title: "Validating Domains",
        description: "Checking domain formats and accessibility...",
        status: "pending"
      },
      {
        id: 2,
        title: "Analyzing Your Site",
        description: `Getting keywords for ${filters.yourDomain}...`,
        status: "pending"
      },
      {
        id: 3,
        title: "Analyzing Competitors",
        description: `Analyzing ${filters.competitors.length} competitor${filters.competitors.length > 1 ? 's' : ''}...`,
        status: "pending"
      },
      {
        id: 4,
        title: "Finding Intersections",
        description: "Comparing keyword rankings across domains...",
        status: "pending"
      },
      {
        id: 5,
        title: "Calculating Gaps",
        description: "Identifying keyword opportunities...",
        status: "pending"
      },
      {
        id: 6,
        title: "Generating Report",
        description: "Formatting your gap analysis results...",
        status: "pending"
      }
    ]
    gapCurrentProgress = 0
    
    // Format the message for gap analysis
    const gapMessage = `Analyze keyword gaps between ${filters.yourDomain} and competitors: ${filters.competitors.join(", ")}. 
    Location: ${filters.location}. 
    Minimum volume: ${filters.minVolume}. 
    Maximum difficulty: ${filters.maxDifficulty}.
    Gap type: ${filters.gapType === 'all' ? 'Show all gaps' : filters.gapType === 'missing' ? 'Keywords competitors rank for but we don\'t' : 'Keywords where competitors outrank us'}.
    Output format: table`
    
    // Override the standard progress steps with gap-specific ones
    progressSteps = gapProgressSteps
    currentProgress = gapCurrentProgress
    
    // Send the message
    input = gapMessage
    
    // Store a reference to parse the response for gap keywords
    const originalMessages = $messages.length
    
    await sendMessage()
    
    // After sendMessage completes, always generate gap analysis results
    // In a real implementation, this would parse the actual agent response
    // For now, we'll generate realistic mock data based on the domains
    
    console.log('Generating gap analysis results for:', filters.yourDomain, 'vs', filters.competitors)
    
    // Generate gap analysis data based on the domains
    const keywordCategories = {
      database: [
        'cloud database solutions', 'distributed database architecture', 'real-time analytics platform',
        'in-memory database performance', 'nosql vs sql comparison', 'database scalability best practices',
        'data warehouse modernization', 'streaming data processing', 'database migration tools',
        'enterprise data management', 'database security best practices', 'multi-tenant database design'
      ],
      general: [
        'enterprise software solutions', 'cloud infrastructure management', 'devops automation tools',
        'api integration platform', 'microservices architecture', 'container orchestration solutions',
        'serverless computing benefits', 'data pipeline automation', 'machine learning operations',
        'software development lifecycle', 'agile project management', 'continuous integration tools'
      ]
    }
    
    // Select keywords based on domain names
    const isDatabase = filters.yourDomain.includes('database') || filters.yourDomain.includes('db') ||
                     filters.competitors.some(c => c.includes('database') || c.includes('db'))
    
    const selectedKeywords = isDatabase ? keywordCategories.database : keywordCategories.general
    
    // Generate gap keywords with realistic data
    const newGapKeywords = selectedKeywords.map((keyword, index) => {
      const isYourSiteRanking = Math.random() > 0.6
      const competitorPos = Math.floor(Math.random() * 20) + 1
      const yourPos = isYourSiteRanking ? competitorPos + Math.floor(Math.random() * 30) + 5 : null
      
      return {
        keyword,
        search_volume: Math.floor(Math.random() * 30000) + 500,
        difficulty: Math.floor(Math.random() * 70) + 20,
        competition: ['LOW', 'MEDIUM', 'HIGH'][Math.floor(Math.random() * 3)] as 'LOW' | 'MEDIUM' | 'HIGH',
        cpc: Math.random() * 8 + 1.5,
        competitor_position: competitorPos,
        your_position: yourPos,
        gap_type: yourPos === null ? 'missing' : 'lower_rank' as 'missing' | 'lower_rank',
        opportunity_score: 0
      }
    })
    
    // Calculate opportunity scores and sort
    gapKeywords = newGapKeywords
      .map(k => ({
        ...k,
        opportunity_score: (k.search_volume / (k.difficulty + 1)) * (k.gap_type === 'missing' ? 2 : 1)
      }))
      .sort((a, b) => b.opportunity_score - a.opportunity_score)
    
    console.log('Gap analysis complete:', gapKeywords.length, 'opportunities found')
    
    // Ensure the array reference is updated for reactivity
    gapKeywords = [...gapKeywords]
  }
</script>

<svelte:head>
  <title>SEO Strategist - AI Agent</title>
</svelte:head>

<div class="h-screen flex flex-col" style="background: var(--background);">
  <!-- Header -->
  <div
    class="border-b-2 flex-shrink-0"
    style="border-color: var(--border); background: var(--background);"
  >
    <div class="max-w-7xl mx-auto px-6 lg:px-8 py-6">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-4">
          <div
            class="w-12 h-12 flex items-center justify-center border-2 hover:scale-105 transition-transform cursor-pointer"
            style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <Target
              class="w-6 h-6 animate-pulse"
              style="color: var(--primary-foreground);"
            />
          </div>
          <div>
            <h1 class="text-3xl font-black" style="color: var(--foreground);">
              Lexi - SEO Strategist
            </h1>
            <p
              class="text-lg font-medium"
              style="color: var(--muted-foreground);"
            >
              Powered by Lexi – your AI SEO Sidekick
            </p>
          </div>
        </div>
        <div class="flex items-center space-x-4">
          <!-- Mode Toggle -->
          <div class="flex border-2" style="border-color: var(--border); background: var(--background);">
            <button
              on:click={() => mode = "chat"}
              class="px-4 py-2 text-sm font-medium transition-colors {mode === 'chat' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
            >
              Chat Mode
            </button>
            <button
              on:click={() => mode = "niche"}
              class="px-4 py-2 text-sm font-medium transition-colors border-l {mode === 'niche' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
              style="border-color: var(--border);"
            >
              <Sparkles class="w-4 h-4 inline mr-1" />
              Niche Discovery
            </button>
            <button
              on:click={() => mode = "gap"}
              class="px-4 py-2 text-sm font-medium transition-colors border-l {mode === 'gap' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
              style="border-color: var(--border);"
            >
              <TrendingUp class="w-4 h-4 inline mr-1" />
              Gap Analysis
            </button>
            <button
              on:click={() => mode = "cluster"}
              class="px-4 py-2 text-sm font-medium transition-colors border-l {mode === 'cluster' ? 'bg-primary text-primary-foreground' : 'text-muted-foreground hover:text-foreground'}"
              style="border-color: var(--border);"
            >
              <Share2 class="w-4 h-4 inline mr-1" />
              Cluster Mapping
            </button>
          </div>
          
          <div
            class="flex items-center space-x-2 px-4 py-2 border-2"
            style="background: var(--accent); border-color: var(--border); box-shadow: var(--shadow-sm);"
          >
            <TrendingUp
              class="w-4 h-4"
              style="color: var(--accent-foreground);"
            />
            <span
              class="text-sm font-bold"
              style="color: var(--accent-foreground);"
              >Keyword Intelligence</span
            >
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Breadcrumb Navigation -->
  <div class="max-w-7xl px-6 lg:px-8 py-4">
    <nav class="flex items-center space-x-2 text-sm text-muted-foreground">
      <a
        href="/dashboard/{$page.params.envSlug}"
        class="hover:text-foreground transition-colors"
      >
        Dashboard
      </a>
      <ChevronRight class="w-4 h-4" />
      <span class="text-foreground font-medium">SEO Agent</span>
    </nav>
  </div>

  <div
    class="flex-1 overflow-hidden max-w-7xl mx-auto px-6 lg:px-8 py-8 w-full"
  >
    {#if mode === "chat"}
      <!-- Full Width Conversation Panel -->
      <div class="h-full">
        <div
          class="card-brutal p-0 chat-container h-full"
          style="background: var(--card);"
        >
          <!-- Messages Area -->
          <div class="messages-wrapper messages-wrapper-seo messages-container">
            <div class="space-y-6">
              {#if $messages.length === 0}
              <div class="text-center py-12">
                <div
                  class="w-16 h-16 mx-auto mb-4 flex items-center justify-center border-2"
                  style="background: var(--muted); border-color: var(--border);"
                >
                  <Target
                    class="w-8 h-8"
                    style="color: var(--muted-foreground);"
                  />
                </div>
                <h3
                  class="text-xl font-bold mb-2"
                  style="color: var(--foreground);"
                >
                  Start Your SEO Research
                </h3>
                <p
                  class="font-medium mb-6"
                  style="color: var(--muted-foreground);"
                >
                  Get keyword analysis and SEO strategy for any business
                </p>

                <!-- Interactive Cards -->
                <div class="grid md:grid-cols-3 gap-4 max-w-4xl mx-auto">
                  {#each interactiveCards as card}
                    <button
                      on:click={() => {
                        input = card.prompt
                      }}
                      class="card-brutal p-4 text-left transition-all duration-200 hover:-translate-x-1 hover:-translate-y-1 group"
                      style="background: var(--card); border-color: var(--border);"
                      disabled={isLoading}
                    >
                      <div class="flex items-center gap-3 mb-3">
                        <div
                          class="w-8 h-8 flex items-center justify-center border-2 group-hover:scale-110 transition-transform"
                          style="background: var(--primary); border-color: var(--border);"
                        >
                          <svelte:component
                            this={card.icon}
                            class="w-4 h-4"
                            style="color: var(--primary-foreground);"
                          />
                        </div>
                        <h4
                          class="font-bold text-sm"
                          style="color: var(--foreground);"
                        >
                          {card.title}
                        </h4>
                      </div>
                      <p
                        class="text-xs mb-3"
                        style="color: var(--muted-foreground);"
                      >
                        {card.description}
                      </p>
                      <div
                        class="text-xs font-mono p-2 border-2 rounded"
                        style="background: var(--muted); border-color: var(--border); color: var(--muted-foreground);"
                      >
                        {card.prompt}
                      </div>
                    </button>
                  {/each}
                </div>
              </div>
            {/if}

            {#each $messages as message}
              <div
                class="flex gap-4 {message.role === 'user'
                  ? 'flex-row-reverse'
                  : ''}"
              >
                <div
                  class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                  style="background: var(--{message.role === 'user'
                    ? 'primary'
                    : 'secondary'}); border-color: var(--border); box-shadow: var(--shadow-sm);"
                >
                  {#if message.role === "user"}
                    <User
                      class="w-5 h-5"
                      style="color: var(--primary-foreground);"
                    />
                  {:else}
                    <Bot
                      class="w-5 h-5"
                      style="color: var(--secondary-foreground);"
                    />
                  {/if}
                </div>

                <div class="flex-1 max-w-3xl">
                  <div class="flex items-center gap-2 mb-2">
                    <span
                      class="text-sm font-bold"
                      style="color: var(--foreground);"
                    >
                      {message.role === "user" ? "You" : "SEO Strategist"}
                    </span>
                    <div class="flex items-center gap-1">
                      <Clock
                        class="w-3 h-3"
                        style="color: var(--muted-foreground);"
                      />
                      <span
                        class="text-xs"
                        style="color: var(--muted-foreground);"
                      >
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                    </div>
                    {#if message.role === "assistant" && message.isReport}
                      <button
                        on:click={() => downloadAsMarkdown(message)}
                        class="btn-secondary px-2 py-1 text-xs flex items-center gap-1"
                        title="Download as Markdown"
                      >
                        <Download class="w-3 h-3" />
                        Download
                      </button>
                    {/if}
                  </div>

                  {#if message.role === "user"}
                    <div
                      class="p-4 border-2"
                      style="background: var(--primary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                    >
                      <p
                        class="font-medium"
                        style="color: var(--primary-foreground);"
                      >
                        {message.content}
                      </p>
                    </div>
                  {:else}
                    <div
                      class="p-6 border-2 mb-4"
                      style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow);"
                    >
                      <div class="prose prose-sm max-w-none">
                        {@html formatContent(message.content)}
                      </div>
                    </div>

                    <!-- Follow-up Actions -->
                    <div class="flex flex-wrap gap-2 mb-4">
                      <button
                        on:click={() =>
                          generateFollowUp("cluster", message.content)}
                        class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                      >
                        <BarChart class="w-3 h-3" />
                        Cluster by Intent
                      </button>
                      <button
                        on:click={() => copyToClipboard(message.content)}
                        class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                      >
                        <Copy class="w-3 h-3" />
                        Copy
                      </button>
                      <button
                        on:click={() => downloadAsCSV(message.content)}
                        class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                      >
                        <FileDown class="w-3 h-3" />
                        Export CSV
                      </button>
                      <button
                        on:click={() =>
                          generateFollowUp("blog", message.content)}
                        class="btn-secondary px-3 py-1 text-xs flex items-center gap-1"
                      >
                        <BookOpen class="w-3 h-3" />
                        Generate Blog Outline
                      </button>
                    </div>
                  {/if}
                </div>
              </div>
            {/each}

            {#if isLoading}
              <div class="flex gap-4">
                <div
                  class="w-10 h-10 flex-shrink-0 flex items-center justify-center border-2"
                  style="background: var(--secondary); border-color: var(--border); box-shadow: var(--shadow-sm);"
                >
                  <Bot
                    class="w-5 h-5"
                    style="color: var(--secondary-foreground);"
                  />
                </div>
                <div class="flex-1">
                  <div class="flex items-center gap-2 mb-2">
                    <span
                      class="text-sm font-bold"
                      style="color: var(--foreground);">SEO Strategist</span
                    >
                    <span
                      class="text-xs"
                      style="color: var(--muted-foreground);"
                      >Working on your analysis...</span
                    >
                  </div>
                  <div
                    class="p-6 border-2"
                    style="background: var(--background); border-color: var(--border); box-shadow: var(--shadow-sm);"
                  >
                    <!-- Progress Timeline -->
                    <div class="space-y-4">
                      {#each progressSteps as step (step.id)}
                        <div
                          class="flex items-start gap-3"
                          transition:slide={{ duration: 300 }}
                        >
                          <div class="flex-shrink-0 mt-0.5">
                            {#if step.status === "completed"}
                              <div transition:fade={{ duration: 200 }}>
                                <CheckCircle2
                                  class="w-5 h-5 animate-scale-in"
                                  style="color: var(--primary);"
                                />
                              </div>
                            {:else if step.status === "active"}
                              <Loader2
                                class="w-5 h-5 animate-spin"
                                style="color: var(--primary);"
                              />
                            {:else}
                              <Circle
                                class="w-5 h-5 opacity-30"
                                style="color: var(--muted-foreground);"
                              />
                            {/if}
                          </div>
                          <div class="flex-1">
                            <h4
                              class="text-sm font-bold mb-1"
                              style="color: {step.status === 'pending'
                                ? 'var(--muted-foreground)'
                                : 'var(--foreground)'};{step.status ===
                              'pending'
                                ? 'opacity: 0.5'
                                : ''}"
                            >
                              {step.title}
                            </h4>
                            <p
                              class="text-xs"
                              style="color: var(--muted-foreground);{step.status ===
                              'pending'
                                ? 'opacity: 0.5'
                                : ''}"
                            >
                              {step.description}
                            </p>
                            {#if step.status === "active" && step.progress}
                              <div
                                class="mt-2 h-1 rounded-full overflow-hidden"
                                style="background: var(--muted);"
                              >
                                <div
                                  class="h-full transition-all duration-500 ease-out"
                                  style="background: var(--primary); width: {step.progress}%"
                                ></div>
                              </div>
                            {/if}
                          </div>
                        </div>
                      {/each}
                    </div>

                    <!-- Overall Progress -->
                    <div
                      class="mt-6 pt-4 border-t"
                      style="border-color: var(--border);"
                    >
                      <div class="flex items-center justify-between mb-2">
                        <span
                          class="text-xs font-medium"
                          style="color: var(--muted-foreground);"
                          >Overall Progress</span
                        >
                        <span
                          class="text-xs font-bold"
                          style="color: var(--foreground);"
                          >{currentProgress}%</span
                        >
                      </div>
                      <div
                        class="h-2 rounded-full overflow-hidden"
                        style="background: var(--muted);"
                      >
                        <div
                          class="h-full transition-all duration-500 ease-out"
                          style="background: linear-gradient(to right, var(--primary), var(--accent)); width: {currentProgress}%"
                        ></div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            {/if}
          </div>
        </div>

        <!-- Input Area -->
        <div
          class="input-wrapper input-wrapper-seo p-6 {isLoading
            ? 'opacity-50 pointer-events-none'
            : ''}"
        >
          <!-- Output Format Toggle -->
          <div
            class="flex items-center justify-between"
            style="margin-bottom: 15px;"
          >
            <h2 class="text-lg font-bold" style="color: var(--foreground);">
              SEO Analysis
            </h2>
            <div class="flex items-center space-x-2">
              <span
                class="text-sm font-medium"
                style="color: var(--muted-foreground);">Output Format:</span
              >
              <div
                class="flex border-2"
                style="border-color: var(--border); background: var(--background);"
              >
                <button
                  on:click={() => (outputFormat = "summary")}
                  class="px-3 py-1 text-sm font-medium transition-colors {outputFormat ===
                  'summary'
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:text-foreground'}"
                  disabled={isLoading}
                >
                  Summary
                </button>
                <button
                  on:click={() => (outputFormat = "table")}
                  class="px-3 py-1 text-sm font-medium transition-colors border-l border-r {outputFormat ===
                  'table'
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:text-foreground'}"
                  style="border-color: var(--border);"
                  disabled={isLoading}
                >
                  Table
                </button>
                <button
                  on:click={() => (outputFormat = "blog")}
                  class="px-3 py-1 text-sm font-medium transition-colors {outputFormat ===
                  'blog'
                    ? 'bg-primary text-primary-foreground'
                    : 'text-muted-foreground hover:text-foreground'}"
                  disabled={isLoading}
                >
                  Blog-ready
                </button>
              </div>
            </div>
          </div>

          <!-- Prompt Enhancer Filters -->
          <div style="margin-bottom: 15px;">
            <button
              on:click={() => (showFilters = !showFilters)}
              class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"
              style="margin-bottom: 10px;"
              disabled={isLoading}
            >
              <Filter class="w-4 h-4" />
              Prompt Enhancer
              <span
                class="text-xs {showFilters
                  ? 'rotate-180'
                  : ''} transition-transform">▼</span
              >
            </button>

            {#if showFilters}
              <div
                class="grid md:grid-cols-3 gap-4 p-4 border-2"
                style="background: var(--muted); border-color: var(--border);"
              >
                <div>
                  <label
                    class="block text-xs font-bold mb-2"
                    style="color: var(--foreground);">Target Audience</label
                  >
                  <select
                    bind:value={targetAudience}
                    class="w-full p-2 border-2 text-xs"
                    style="background: var(--background); border-color: var(--border); color: var(--foreground);"
                    disabled={isLoading}
                  >
                    <option value="">Select audience</option>
                    <option value="B2B SaaS">B2B SaaS</option>
                    <option value="E-commerce">E-commerce</option>
                    <option value="Local business">Local business</option>
                    <option value="Content creators">Content creators</option>
                    <option value="Enterprise">Enterprise</option>
                  </select>
                </div>
                <div>
                  <label
                    class="block text-xs font-bold mb-2"
                    style="color: var(--foreground);">Region Focus</label
                  >
                  <input
                    bind:value={regionFocus}
                    placeholder="e.g., US, Europe, Global"
                    class="w-full p-2 border-2 text-xs"
                    style="background: var(--background); border-color: var(--border); color: var(--foreground);"
                    disabled={isLoading}
                  />
                </div>
                <div>
                  <label
                    class="block text-xs font-bold mb-2"
                    style="color: var(--foreground);">Funnel Stage</label
                  >
                  <div
                    class="flex border-2"
                    style="border-color: var(--border); background: var(--background);"
                  >
                    <button
                      on:click={() => (funnelStage = "awareness")}
                      class="px-2 py-1 text-xs font-medium transition-colors {funnelStage ===
                      'awareness'
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground'}"
                      disabled={isLoading}
                    >
                      Awareness
                    </button>
                    <button
                      on:click={() => (funnelStage = "consideration")}
                      class="px-2 py-1 text-xs font-medium transition-colors border-l border-r {funnelStage ===
                      'consideration'
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground'}"
                      style="border-color: var(--border);"
                      disabled={isLoading}
                    >
                      Consideration
                    </button>
                    <button
                      on:click={() => (funnelStage = "decision")}
                      class="px-2 py-1 text-xs font-medium transition-colors {funnelStage ===
                      'decision'
                        ? 'bg-primary text-primary-foreground'
                        : 'text-muted-foreground hover:text-foreground'}"
                      disabled={isLoading}
                    >
                      Decision
                    </button>
                  </div>
                </div>
              </div>
            {/if}
          </div>

          <div class="flex" style="gap: 15px;">
            <div class="flex-1 relative">
              <textarea
                bind:value={input}
                on:keydown={handleKeyDown}
                placeholder={currentPlaceholder}
                class="input-brutal enhanced-input flex-1 resize-none p-4 w-full h-full"
                style="min-height: 60px;"
                disabled={isLoading}
              ></textarea>
            </div>
            <button
              on:click={sendMessage}
              disabled={!input.trim() || isLoading}
              class="btn-primary px-6 font-bold flex items-center gap-2"
              style="height: auto; align-self: stretch;"
            >
              {#if isLoading}
                <div
                  class="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"
                ></div>
              {:else}
                <Target class="w-4 h-4 animate-pulse" />
              {/if}
              Analyze
            </button>
          </div>
        </div>
      </div>
    </div>
    {:else if mode === "niche"}
      <!-- Niche Discovery Mode -->
      <div class="h-full">
        <div
          class="card-brutal p-6 h-full overflow-y-auto"
          style="background: var(--card);"
        >
          <NicheDiscovery 
            onAnalyze={handleNicheDiscovery}
            {isLoading}
            discoveredKeywords={nicheKeywords}
          />
        </div>
      </div>
    {:else if mode === "gap"}
      <!-- Competitor Gap Analysis Mode -->
      <div class="h-full">
        <div
          class="card-brutal p-6 h-full overflow-y-auto"
          style="background: var(--card);"
        >
          <CompetitorGapAnalysis 
            onAnalyze={handleGapAnalysis}
            {isLoading}
            gapKeywords={gapKeywords}
            progressSteps={gapProgressSteps}
            currentProgress={gapCurrentProgress}
          />
        </div>
      </div>
    {:else if mode === "cluster"}
      <!-- Content Cluster Mapping Mode -->
      <div class="h-full">
        <div
          class="card-brutal p-6 h-full overflow-y-auto"
          style="background: var(--card);"
        >
          <ContentClusterMapping 
            onAnalyze={handleClusterMapping}
            {isLoading}
            {contentNodes}
            {contentEdges}
            progressSteps={clusterProgressSteps}
            currentProgress={clusterCurrentProgress}
          />
        </div>
      </div>
    {/if}
  </div>
</div>
