<script lang="ts">
  import { onMount, onDestroy } from 'svelte'
  import { 
    Network,
    Share2,
    Download,
    ZoomIn,
    ZoomOut,
    Maximize2,
    Loader2,
    Settings,
    Info,
    X
  } from 'lucide-svelte'
  import { slide } from 'svelte/transition'
  import { Network as VisNetwork, type Options } from 'vis-network'
  
  interface ContentNode {
    id: string
    label: string
    title: string
    type: 'pillar' | 'cluster' | 'supporting'
    searchVolume: number
    difficulty: number
    keywords: string[]
    level: number
    group: number
  }
  
  interface ContentEdge {
    from: string
    to: string
    strength: number
    label?: string
  }
  
  interface ClusterFilters {
    mainTopic: string
    businessType: string
    targetAudience: string
    depth: number
  }
  
  interface ProgressStep {
    id: number
    title: string
    description: string
    status: 'pending' | 'active' | 'completed'
    progress?: number
  }
  
  interface Props {
    onAnalyze: (filters: ClusterFilters) => void
    isLoading?: boolean
    contentNodes?: ContentNode[]
    contentEdges?: ContentEdge[]
    progressSteps?: ProgressStep[]
    currentProgress?: number
  }
  
  let { 
    onAnalyze, 
    isLoading = false,
    contentNodes = [],
    contentEdges = [],
    progressSteps = [],
    currentProgress = 0
  }: Props = $props()
  
  let mainTopic = $state('')
  let businessType = $state('')
  let targetAudience = $state('')
  let depth = $state(3)
  let showSettings = $state(false)
  let selectedNode = $state<ContentNode | null>(null)
  
  let container = $state<HTMLDivElement>()
  let network = $state<VisNetwork | null>(null)
  
  // Network options
  const getNetworkOptions = (isDarkMode: boolean): Options => ({
    nodes: {
      shape: 'dot',
      font: {
        size: 14,
        color: isDarkMode ? '#ffffff' : '#000000',
        face: 'Inter, sans-serif'
      },
      borderWidth: 2,
      shadow: true
    },
    edges: {
      width: 2,
      color: { inherit: 'from' },
      smooth: {
        enabled: true,
        type: 'continuous',
        roundness: 0.5
      },
      arrows: {
        to: {
          enabled: true,
          scaleFactor: 0.5
        }
      },
      font: {
        color: isDarkMode ? '#a3a3a3' : '#525252',
        align: 'middle'
      }
    },
    physics: {
      enabled: true,
      solver: 'forceAtlas2Based',
      forceAtlas2Based: {
        gravitationalConstant: -50,
        centralGravity: 0.01,
        springLength: 100,
        springConstant: 0.08
      },
      stabilization: {
        enabled: true,
        iterations: 1000,
        updateInterval: 50
      }
    },
    layout: {
      randomSeed: 2,
      improvedLayout: true
    },
    interaction: {
      hover: true,
      tooltipDelay: 200,
      hideEdgesOnDrag: true
    }
  })
  
  function handleAnalyze() {
    if (!mainTopic.trim()) return
    
    const filters: ClusterFilters = {
      mainTopic: mainTopic.trim(),
      businessType,
      targetAudience,
      depth
    }
    
    onAnalyze(filters)
  }
  
  function getNodeColor(node: ContentNode): string {
    if (node.type === 'pillar') return '#2563eb' // blue-600
    if (node.type === 'cluster') return '#059669' // emerald-600
    return '#7c3aed' // violet-600
  }
  
  function getNodeSize(node: ContentNode): number {
    if (node.type === 'pillar') return 40
    if (node.type === 'cluster') return 30
    return 20
  }
  
  function createNetwork() {
    if (!container || contentNodes.length === 0) return
    
    // Check if dark mode to adjust text colors
    const isDarkMode = document.documentElement.getAttribute('data-theme') === 'dark' ||
                      window.matchMedia('(prefers-color-scheme: dark)').matches
    
    const textColor = isDarkMode ? '#e5e5e5' : '#171717'
    const mutedTextColor = isDarkMode ? '#a3a3a3' : '#737373'
    
    // Transform nodes for vis-network
    const visNodes = contentNodes.map(node => ({
      id: node.id,
      label: node.label,
      title: `${node.title}\nVolume: ${node.searchVolume.toLocaleString()} | Difficulty: ${node.difficulty}/100`,
      color: {
        background: getNodeColor(node),
        border: getNodeColor(node),
        highlight: {
          background: getNodeColor(node),
          border: '#000000'
        }
      },
      size: getNodeSize(node),
      font: {
        color: isDarkMode ? '#ffffff' : '#000000'
      },
      group: node.group.toString(),
      level: node.level
    }))
    
    // Transform edges for vis-network
    const visEdges = contentEdges.map(edge => ({
      from: edge.from,
      to: edge.to,
      width: edge.strength * 5,
      label: edge.label,
      font: { 
        size: 10, 
        color: isDarkMode ? '#a3a3a3' : '#525252'
      }
    }))
    
    // Create network
    network = new VisNetwork(container, {
      nodes: visNodes,
      edges: visEdges
    }, getNetworkOptions(isDarkMode))
    
    // Handle node selection
    network.on('selectNode', (params) => {
      if (params.nodes.length > 0) {
        const nodeId = params.nodes[0]
        selectedNode = contentNodes.find(n => n.id === nodeId) || null
      }
    })
    
    network.on('deselectNode', () => {
      selectedNode = null
    })
  }
  
  function zoomIn() {
    if (network) {
      const scale = network.getScale()
      network.moveTo({ scale: scale * 1.2 })
    }
  }
  
  function zoomOut() {
    if (network) {
      const scale = network.getScale()
      network.moveTo({ scale: scale * 0.8 })
    }
  }
  
  function fitNetwork() {
    if (network) {
      network.fit({ animation: true })
    }
  }
  
  function exportAsImage() {
    if (!network) return
    
    const canvas = container?.querySelector('canvas')
    if (canvas) {
      const url = canvas.toDataURL('image/png')
      const a = document.createElement('a')
      a.href = url
      a.download = `content-cluster-${mainTopic.replace(/\s+/g, '-')}.png`
      a.click()
    }
  }
  
  // Reactive update when data changes
  $effect(() => {
    if (contentNodes.length > 0 && container) {
      if (network) {
        network.destroy()
      }
      createNetwork()
    }
  })
  
  onMount(() => {
    if (contentNodes.length > 0 && container) {
      createNetwork()
    }
  })
  
  onDestroy(() => {
    if (network) {
      network.destroy()
    }
  })
</script>

<div class="space-y-6">
  <!-- Header -->
  <div class="flex items-center justify-between">
    <div class="flex items-center gap-3">
      <div class="w-10 h-10 flex items-center justify-center border-2" 
           style="background: var(--primary); border-color: var(--border);">
        <Share2 class="w-5 h-5" style="color: var(--primary-foreground);" />
      </div>
      <div>
        <h3 class="text-lg font-bold" style="color: var(--foreground);">
          Content Cluster Mapping
        </h3>
        <p class="text-sm" style="color: var(--muted-foreground);">
          Build topical authority with strategic content clusters
        </p>
      </div>
    </div>
  </div>
  
  <!-- Input Section -->
  <div class="space-y-4">
    <div>
      <label class="block text-sm font-medium mb-2" style="color: var(--foreground);">
        Main Topic or Seed Keyword
      </label>
      <input
        bind:value={mainTopic}
        placeholder="e.g., project management, content marketing, web development"
        class="w-full p-3 border-2"
        style="background: var(--background); border-color: var(--border); color: var(--foreground);"
        disabled={isLoading}
      />
    </div>
    
    <!-- Settings -->
    <div>
      <button
        onclick={() => showSettings = !showSettings}
        class="btn-secondary px-3 py-2 text-sm flex items-center gap-2"
        disabled={isLoading}
      >
        <Settings class="w-4 h-4" />
        Advanced Settings
        <span class="text-xs {showSettings ? 'rotate-180' : ''} transition-transform">▼</span>
      </button>
      
      {#if showSettings}
        <div class="mt-4 p-4 border-2 space-y-4" 
             style="background: var(--muted); border-color: var(--border);">
          <div class="grid md:grid-cols-2 gap-4">
            <div>
              <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">
                Business Type
              </label>
              <input
                bind:value={businessType}
                placeholder="e.g., SaaS, E-commerce, Agency"
                class="w-full p-2 border-2 text-sm"
                style="background: var(--background); border-color: var(--border); color: var(--foreground);"
                disabled={isLoading}
              />
            </div>
            <div>
              <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">
                Target Audience
              </label>
              <input
                bind:value={targetAudience}
                placeholder="e.g., Small businesses, Enterprises"
                class="w-full p-2 border-2 text-sm"
                style="background: var(--background); border-color: var(--border); color: var(--foreground);"
                disabled={isLoading}
              />
            </div>
          </div>
          <div>
            <label class="block text-xs font-bold mb-2" style="color: var(--foreground);">
              Content Depth: {depth} levels
            </label>
            <input
              type="range"
              bind:value={depth}
              min="1"
              max="5"
              class="w-full"
              disabled={isLoading}
            />
            <div class="flex justify-between text-xs mt-1" style="color: var(--muted-foreground);">
              <span>Simple</span>
              <span>Comprehensive</span>
            </div>
          </div>
        </div>
      {/if}
    </div>
  </div>
  
  <!-- Analyze Button -->
  <button
    onclick={handleAnalyze}
    disabled={!mainTopic.trim() || isLoading}
    class="btn-primary px-6 py-3 font-bold flex items-center gap-2 w-full md:w-auto"
  >
    {#if isLoading}
      <Loader2 class="w-4 h-4 animate-spin" />
      Creating Content Clusters...
    {:else}
      <Network class="w-4 h-4" />
      Generate Content Clusters
    {/if}
  </button>
  
  <!-- Progress Tracking -->
  {#if isLoading && progressSteps.length > 0}
    <div class="border-2 p-6" 
         style="background: var(--card); border-color: var(--border);"
         transition:slide={{ duration: 300 }}>
      <h4 class="font-bold mb-4" style="color: var(--foreground);">
        Building Content Clusters
      </h4>
      
      <div class="space-y-3">
        {#each progressSteps as step (step.id)}
          <div class="flex items-center gap-3">
            <div class="w-8 h-8 flex items-center justify-center border-2 rounded-full"
                 style="background: {step.status === 'completed' ? 'var(--primary)' : step.status === 'active' ? 'var(--accent)' : 'var(--muted)'}; 
                        border-color: var(--border);">
              {#if step.status === 'completed'}
                <span style="color: var(--primary-foreground);">✓</span>
              {:else if step.status === 'active'}
                <Loader2 class="w-4 h-4 animate-spin" style="color: var(--accent-foreground);" />
              {:else}
                <span style="color: var(--muted-foreground);">{step.id}</span>
              {/if}
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium" 
                 style="color: {step.status === 'pending' ? 'var(--muted-foreground)' : 'var(--foreground)'};">
                {step.title}
              </p>
              <p class="text-xs" style="color: var(--muted-foreground);">
                {step.description}
              </p>
            </div>
          </div>
        {/each}
      </div>
      
      <div class="mt-4 h-2 rounded-full overflow-hidden" style="background: var(--muted);">
        <div class="h-full transition-all duration-500" 
             style="background: var(--primary); width: {currentProgress}%;">
        </div>
      </div>
    </div>
  {/if}
  
  <!-- Visualization -->
  {#if contentNodes.length > 0 && !isLoading}
    <div class="border-2" style="background: var(--card); border-color: var(--border);">
      <!-- Controls -->
      <div class="p-4 border-b-2 flex items-center justify-between" style="border-color: var(--border);">
        <h4 class="font-bold" style="color: var(--foreground);">
          Content Cluster Visualization
        </h4>
        <div class="flex gap-2">
          <button
            onclick={zoomIn}
            class="btn-secondary p-2"
            title="Zoom In"
          >
            <ZoomIn class="w-4 h-4" />
          </button>
          <button
            onclick={zoomOut}
            class="btn-secondary p-2"
            title="Zoom Out"
          >
            <ZoomOut class="w-4 h-4" />
          </button>
          <button
            onclick={fitNetwork}
            class="btn-secondary p-2"
            title="Fit to Screen"
          >
            <Maximize2 class="w-4 h-4" />
          </button>
          <button
            onclick={exportAsImage}
            class="btn-secondary p-2"
            title="Export as Image"
          >
            <Download class="w-4 h-4" />
          </button>
        </div>
      </div>
      
      <!-- Network Container -->
      <div class="relative" style="height: 600px; background: var(--card);">
        <div bind:this={container} class="w-full h-full" style="background: var(--background);"></div>
        
        <!-- Legend -->
        <div class="absolute bottom-4 left-4 p-3 border-2" 
             style="background: var(--background); border-color: var(--border);">
          <p class="text-xs font-bold mb-2" style="color: var(--foreground);">Legend</p>
          <div class="space-y-1">
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 rounded-full" style="background: #2563eb;"></div>
              <span class="text-xs" style="color: var(--muted-foreground);">Pillar Content</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 rounded-full" style="background: #059669;"></div>
              <span class="text-xs" style="color: var(--muted-foreground);">Content Clusters</span>
            </div>
            <div class="flex items-center gap-2">
              <div class="w-4 h-4 rounded-full" style="background: #7c3aed;"></div>
              <span class="text-xs" style="color: var(--muted-foreground);">Supporting Content</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Node Details Panel -->
      {#if selectedNode}
        <div class="p-6 border-t-2" style="border-color: var(--border); background: var(--muted);" transition:slide>
          <div class="space-y-4">
            <!-- Header -->
            <div class="flex items-start justify-between">
              <div>
                <h5 class="text-lg font-bold" style="color: var(--foreground);">
                  {selectedNode.title}
                </h5>
                <div class="flex items-center gap-2 mt-1">
                  <span class="px-2 py-1 text-xs rounded-full font-medium" 
                        style="background: {getNodeColor(selectedNode)}; color: white;">
                    {selectedNode.type === 'pillar' ? 'Pillar Content' : 
                     selectedNode.type === 'cluster' ? 'Content Cluster' : 'Supporting Content'}
                  </span>
                </div>
              </div>
              <button 
                onclick={() => selectedNode = null}
                class="p-1 hover:bg-black/10 rounded"
              >
                <X class="w-4 h-4" style="color: var(--muted-foreground);" />
              </button>
            </div>

            <!-- Metrics -->
            <div class="grid md:grid-cols-2 gap-4">
              <div class="space-y-2">
                <div>
                  <p class="text-xs font-medium" style="color: var(--muted-foreground);">Search Volume</p>
                  <p class="text-2xl font-bold" style="color: var(--foreground);">
                    {selectedNode.searchVolume.toLocaleString()}
                  </p>
                </div>
              </div>
              <div class="space-y-2">
                <div>
                  <p class="text-xs font-medium" style="color: var(--muted-foreground);">Difficulty Score</p>
                  <div class="flex items-center gap-2">
                    <div class="flex-1 h-2 rounded-full overflow-hidden" style="background: var(--border);">
                      <div class="h-full transition-all duration-300" 
                           style="background: {selectedNode.difficulty < 30 ? '#10b981' : 
                                             selectedNode.difficulty < 60 ? '#f59e0b' : '#ef4444'}; 
                                  width: {selectedNode.difficulty}%;">
                      </div>
                    </div>
                    <span class="text-sm font-bold" style="color: var(--foreground);">
                      {selectedNode.difficulty}/100
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Keywords -->
            <div>
              <p class="text-xs font-medium mb-2" style="color: var(--muted-foreground);">Target Keywords</p>
              <div class="flex flex-wrap gap-2">
                {#each selectedNode.keywords as keyword}
                  <span class="px-3 py-1 text-xs border rounded-full" 
                        style="background: var(--background); border-color: var(--border); color: var(--foreground);">
                    {keyword}
                  </span>
                {/each}
              </div>
            </div>

            <!-- Content Strategy Tip -->
            <div class="p-3 rounded-lg" style="background: var(--accent); border: 1px solid var(--border);">
              <p class="text-xs font-medium" style="color: var(--accent-foreground);">
                💡 Content Strategy Tip
              </p>
              <p class="text-xs mt-1" style="color: var(--accent-foreground);">
                {#if selectedNode.type === 'pillar'}
                  This pillar content should be comprehensive and link to all related cluster content.
                {:else if selectedNode.type === 'cluster'}
                  This cluster content should dive deep into the specific subtopic and link back to the pillar.
                {:else}
                  This supporting content should answer specific questions and link to the parent cluster.
                {/if}
              </p>
            </div>
          </div>
        </div>
      {/if}
    </div>
  {/if}
</div>